#!/usr/bin/env python3
"""
测试LangGraph流式处理的正确实现

基于官方文档的最佳实践，测试不同的stream_mode：
1. "messages" - 流式输出LLM的token
2. "updates" - 流式输出节点状态更新
3. "custom" - 流式输出自定义数据
4. 多模式组合 - ["messages", "updates"]
"""

import asyncio
import sys
import os
import json
from typing import Any, Dict, List, Optional, TypedDict, Annotated
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from langgraph.config import get_stream_writer

from backend.app.iot.service.ai_chat_service import AIChatService
from loguru import logger

# 定义状态
class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    user_id: int

# 定义工具
@tool
def calculator(expression: str) -> str:
    """计算数学表达式的结果"""
    writer = get_stream_writer()
    
    # 发送自定义流式数据
    writer({"type": "tool_progress", "message": f"开始计算: {expression}"})
    
    try:
        # 简单的数学计算
        result = eval(expression)
        writer({"type": "tool_progress", "message": f"计算完成，结果: {result}"})
        return f"计算结果: {result}"
    except Exception as e:
        error_msg = f"计算错误: {str(e)}"
        writer({"type": "tool_error", "message": error_msg})
        return error_msg

@tool
def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    writer = get_stream_writer()
    
    writer({"type": "tool_progress", "message": f"正在查询 {city} 的天气..."})
    
    # 模拟天气查询
    import time
    time.sleep(1)  # 模拟网络延迟
    
    weather_data = f"{city}今天天气晴朗，温度25°C"
    writer({"type": "tool_progress", "message": f"天气查询完成: {weather_data}"})
    
    return weather_data

class LangGraphStreamingTest:
    """LangGraph流式处理测试类"""
    
    def __init__(self):
        self.ai_chat_service = AIChatService()
        self.tools = [calculator, get_weather]
        self.graph = None
        self.checkpointer = MemorySaver()
    
    async def initialize(self):
        """初始化测试环境"""
        logger.info("初始化LangGraph流式测试...")
        
        # 构建状态图
        await self._build_graph()
        
        logger.info("LangGraph流式测试初始化完成")
    
    async def _build_graph(self):
        """构建LangGraph状态图"""
        # 创建工具节点
        tool_node = ToolNode(self.tools)
        
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("agent", self._call_model)
        workflow.add_node("tools", tool_node)
        
        # 设置入口点
        workflow.add_edge(START, "agent")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": END,
            },
        )
        
        # 工具执行后返回智能体
        workflow.add_edge("tools", "agent")
        
        # 编译图
        self.graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info(f"LangGraph状态图构建完成，包含 {len(self.tools)} 个工具")
    
    async def _call_model(self, state: AgentState, config) -> Dict[str, Any]:
        """调用语言模型节点"""
        try:
            # 构建系统提示
            system_message = SystemMessage(
                content="你是一个智能助手，可以使用提供的工具来帮助用户解决问题。"
                "可用工具：calculator(计算数学表达式)、get_weather(查询天气)。"
                "请根据用户的问题选择合适的工具，并提供准确的回答。"
            )
            
            # 准备消息列表
            messages = [system_message] + list(state["messages"])
            
            # 获取LangChain模型
            model = self.ai_chat_service._get_langchain_model(
                self.ai_chat_service.config.default_model
            )
            model_with_tools = model.bind_tools(self.tools)
            
            # 调用模型
            response = await model_with_tools.ainvoke(messages)
            
            return {"messages": [response]}
            
        except Exception as e:
            logger.error(f"调用模型失败: {e}")
            error_message = AIMessage(content=f"抱歉，处理您的请求时出现错误: {str(e)}")
            return {"messages": [error_message]}
    
    def _should_continue(self, state: AgentState) -> str:
        """判断是否继续执行工具"""
        messages = state["messages"]
        last_message = messages[-1]
        
        # 如果最后一条消息包含工具调用，则继续执行工具
        if hasattr(last_message, "tool_calls") and last_message.tool_calls:
            return "continue"
        else:
            return "end"
    
    async def test_stream_mode_messages(self, message: str):
        """测试 stream_mode="messages" - 流式输出LLM token"""
        print("\n" + "="*60)
        print("🧪 测试 stream_mode='messages' (LLM token流)")
        print("="*60)
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "session_id": "test_session_messages",
            "user_id": 1
        }
        
        config = {
            "configurable": {
                "thread_id": "test_thread_messages",
                "user_id": 1
            }
        }
        
        print(f"📝 用户消息: {message}")
        print("\n🔄 流式输出 (messages模式):")
        
        async for token, metadata in self.graph.astream(
            initial_state,
            stream_mode="messages",
            config=config
        ):
            print(f"Token: {token}")
            print(f"Metadata: {metadata}")
            print("-" * 40)
    
    async def test_stream_mode_updates(self, message: str):
        """测试 stream_mode="updates" - 流式输出节点状态更新"""
        print("\n" + "="*60)
        print("🧪 测试 stream_mode='updates' (节点状态更新)")
        print("="*60)
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "session_id": "test_session_updates",
            "user_id": 1
        }
        
        config = {
            "configurable": {
                "thread_id": "test_thread_updates",
                "user_id": 1
            }
        }
        
        print(f"📝 用户消息: {message}")
        print("\n🔄 流式输出 (updates模式):")
        
        async for chunk in self.graph.astream(
            initial_state,
            stream_mode="updates",
            config=config
        ):
            print(f"Update: {chunk}")
            print("-" * 40)
    
    async def test_stream_mode_custom(self, message: str):
        """测试 stream_mode="custom" - 流式输出自定义数据"""
        print("\n" + "="*60)
        print("🧪 测试 stream_mode='custom' (自定义数据流)")
        print("="*60)
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "session_id": "test_session_custom",
            "user_id": 1
        }
        
        config = {
            "configurable": {
                "thread_id": "test_thread_custom",
                "user_id": 1
            }
        }
        
        print(f"📝 用户消息: {message}")
        print("\n🔄 流式输出 (custom模式):")
        
        async for chunk in self.graph.astream(
            initial_state,
            stream_mode="custom",
            config=config
        ):
            print(f"Custom: {chunk}")
            print("-" * 40)
    
    async def test_stream_mode_multiple(self, message: str):
        """测试多模式组合 - ["messages", "updates", "custom"]"""
        print("\n" + "="*60)
        print("🧪 测试多模式组合 ['messages', 'updates', 'custom']")
        print("="*60)
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "session_id": "test_session_multiple",
            "user_id": 1
        }
        
        config = {
            "configurable": {
                "thread_id": "test_thread_multiple",
                "user_id": 1
            }
        }
        
        print(f"📝 用户消息: {message}")
        print("\n🔄 流式输出 (多模式):")
        
        async for stream_mode, chunk in self.graph.astream(
            initial_state,
            stream_mode=["messages", "updates", "custom"],
            config=config
        ):
            print(f"Mode: {stream_mode}")
            print(f"Chunk: {chunk}")
            print("-" * 40)

async def main():
    """主测试函数"""
    print("🚀 开始LangGraph流式处理测试")
    
    # 创建测试实例
    test = LangGraphStreamingTest()
    
    # 初始化
    await test.initialize()
    
    # 测试消息
    test_message = "计算 3 + 2 * 5"
    
    try:
        # 测试不同的流式模式
        await test.test_stream_mode_updates(test_message)
        await test.test_stream_mode_messages(test_message)
        await test.test_stream_mode_custom(test_message)
        await test.test_stream_mode_multiple(test_message)
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        print(f"\n❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
