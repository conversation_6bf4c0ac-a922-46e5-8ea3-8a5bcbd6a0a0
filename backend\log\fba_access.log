2025-09-10 00:04:19.405 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | 成功认证Java用户: admin
2025-09-10 00:04:20.070 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:04:20.072 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.461ms
2025-09-10 00:09:19.420 | INFO     | bf102a402ec245c8a738259106004d13 | 成功认证Java用户: admin
2025-09-10 00:09:20.087 | INFO     | bf102a402ec245c8a738259106004d13 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:09:20.088 | INFO     | bf102a402ec245c8a738259106004d13 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.902ms
2025-09-10 00:14:19.404 | INFO     | 112999cebb7e42daad442ee37dd0e583 | 成功认证Java用户: admin
2025-09-10 00:14:20.062 | INFO     | 112999cebb7e42daad442ee37dd0e583 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:14:20.065 | INFO     | 112999cebb7e42daad442ee37dd0e583 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 664.379ms
2025-09-10 00:19:19.416 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | 成功认证Java用户: admin
2025-09-10 00:19:20.095 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:19:20.098 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.719ms
2025-09-10 00:24:19.414 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | 成功认证Java用户: admin
2025-09-10 00:24:20.061 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:24:20.063 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.046ms
2025-09-10 00:29:19.408 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | 成功认证Java用户: admin
2025-09-10 00:29:20.076 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:29:20.079 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.029ms
2025-09-10 00:34:19.403 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | 成功认证Java用户: admin
2025-09-10 00:34:20.058 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:34:20.060 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.231ms
2025-09-10 00:39:19.411 | INFO     | 393ba16a94854bfb92a273112427c645 | 成功认证Java用户: admin
2025-09-10 00:39:20.078 | INFO     | 393ba16a94854bfb92a273112427c645 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:39:20.080 | INFO     | 393ba16a94854bfb92a273112427c645 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 672.020ms
2025-09-10 00:44:19.409 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | 成功认证Java用户: admin
2025-09-10 00:44:20.062 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:44:20.064 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.087ms
2025-09-10 00:49:19.408 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | 成功认证Java用户: admin
2025-09-10 00:49:20.055 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:49:20.057 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.488ms
2025-09-10 00:54:19.424 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | 成功认证Java用户: admin
2025-09-10 00:54:20.077 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:54:20.080 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.483ms
2025-09-10 00:59:19.411 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | 成功认证Java用户: admin
2025-09-10 00:59:20.089 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:59:20.091 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.553ms
2025-09-10 01:04:19.407 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | 成功认证Java用户: admin
2025-09-10 01:04:20.074 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:04:20.076 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.558ms
2025-09-10 01:09:19.411 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | 成功认证Java用户: admin
2025-09-10 01:09:20.088 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:09:20.089 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 680.465ms
2025-09-10 01:14:19.410 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | 成功认证Java用户: admin
2025-09-10 01:14:20.060 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:14:20.062 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.941ms
2025-09-10 01:19:19.416 | INFO     | d54d795254b546ad8f93ac71636193e9 | 成功认证Java用户: admin
2025-09-10 01:19:20.100 | INFO     | d54d795254b546ad8f93ac71636193e9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:19:20.103 | INFO     | d54d795254b546ad8f93ac71636193e9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.548ms
2025-09-10 01:24:19.403 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | 成功认证Java用户: admin
2025-09-10 01:24:20.057 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:24:20.058 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.060ms
2025-09-10 01:29:19.410 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | 成功认证Java用户: admin
2025-09-10 01:29:20.088 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:29:20.091 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.063ms
2025-09-10 01:34:19.421 | INFO     | 880658f1afdb471c9f4634955eef66ef | 成功认证Java用户: admin
2025-09-10 01:34:20.076 | INFO     | 880658f1afdb471c9f4634955eef66ef | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:34:20.078 | INFO     | 880658f1afdb471c9f4634955eef66ef | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.854ms
2025-09-10 01:39:19.407 | INFO     | eb9c239d73a9410d84702a618ab868d6 | 成功认证Java用户: admin
2025-09-10 01:39:20.098 | INFO     | eb9c239d73a9410d84702a618ab868d6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:39:20.100 | INFO     | eb9c239d73a9410d84702a618ab868d6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 695.622ms
2025-09-10 01:44:19.410 | INFO     | 28487f6045f749519a3e5e897887b1d7 | 成功认证Java用户: admin
2025-09-10 01:44:20.099 | INFO     | 28487f6045f749519a3e5e897887b1d7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:44:20.100 | INFO     | 28487f6045f749519a3e5e897887b1d7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.993ms
2025-09-10 01:49:19.414 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | 成功认证Java用户: admin
2025-09-10 01:49:20.076 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:49:20.079 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.479ms
2025-09-10 01:54:19.405 | INFO     | 95c44599552246bbb771502c19974ced | 成功认证Java用户: admin
2025-09-10 01:54:20.059 | INFO     | 95c44599552246bbb771502c19974ced | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:54:20.062 | INFO     | 95c44599552246bbb771502c19974ced | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.167ms
2025-09-10 01:59:19.402 | INFO     | 660fd44b3760482db568698c09995a7c | 成功认证Java用户: admin
2025-09-10 01:59:20.056 | INFO     | 660fd44b3760482db568698c09995a7c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:59:20.059 | INFO     | 660fd44b3760482db568698c09995a7c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.163ms
2025-09-10 02:04:19.416 | INFO     | 113b0e1cd36747f790c51459631db0cc | 成功认证Java用户: admin
2025-09-10 02:04:20.048 | INFO     | 113b0e1cd36747f790c51459631db0cc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:04:20.052 | INFO     | 113b0e1cd36747f790c51459631db0cc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 638.663ms
2025-09-10 02:09:19.414 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | 成功认证Java用户: admin
2025-09-10 02:09:20.085 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:09:20.086 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.987ms
2025-09-10 02:14:19.424 | INFO     | 80cad69d081342dc8902d5a245583b42 | 成功认证Java用户: admin
2025-09-10 02:14:20.082 | INFO     | 80cad69d081342dc8902d5a245583b42 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:14:20.084 | INFO     | 80cad69d081342dc8902d5a245583b42 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 662.854ms
2025-09-10 02:19:19.414 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | 成功认证Java用户: admin
2025-09-10 02:19:20.059 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:19:20.063 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 651.601ms
2025-09-10 02:24:19.403 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | 成功认证Java用户: admin
2025-09-10 02:24:20.073 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:24:20.075 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 675.190ms
2025-09-10 02:29:19.411 | INFO     | b0a036ddc5a54959880409806daf725d | 成功认证Java用户: admin
2025-09-10 02:29:20.089 | INFO     | b0a036ddc5a54959880409806daf725d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:29:20.092 | INFO     | b0a036ddc5a54959880409806daf725d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.288ms
2025-09-10 02:34:19.413 | INFO     | 5914044d0354421c941f5b29e5b39417 | 成功认证Java用户: admin
2025-09-10 02:34:20.074 | INFO     | 5914044d0354421c941f5b29e5b39417 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:34:20.076 | INFO     | 5914044d0354421c941f5b29e5b39417 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 665.989ms
2025-09-10 02:39:19.411 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | 成功认证Java用户: admin
2025-09-10 02:39:20.083 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:39:20.085 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 676.076ms
2025-09-10 02:44:19.411 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | 成功认证Java用户: admin
2025-09-10 02:44:20.098 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:44:20.101 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.266ms
2025-09-10 02:49:19.401 | INFO     | 563846eb6fe945a48c77208a52982f52 | 成功认证Java用户: admin
2025-09-10 02:49:20.047 | INFO     | 563846eb6fe945a48c77208a52982f52 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:49:20.049 | INFO     | 563846eb6fe945a48c77208a52982f52 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.463ms
2025-09-10 02:54:19.409 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | 成功认证Java用户: admin
2025-09-10 02:54:20.065 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:54:20.068 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 660.189ms
2025-09-10 02:59:19.405 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | 成功认证Java用户: admin
2025-09-10 02:59:20.049 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:59:20.051 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.734ms
2025-09-10 03:04:19.420 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | 成功认证Java用户: admin
2025-09-10 03:04:20.069 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:04:20.072 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.964ms
2025-09-10 03:09:19.412 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | 成功认证Java用户: admin
2025-09-10 03:09:20.073 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:09:20.074 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 665.188ms
2025-09-10 03:14:19.403 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | 成功认证Java用户: admin
2025-09-10 03:14:20.045 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:14:20.047 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 646.983ms
2025-09-10 03:19:19.413 | INFO     | c5cb85bf7f444622ae06be452295738f | 成功认证Java用户: admin
2025-09-10 03:19:20.089 | INFO     | c5cb85bf7f444622ae06be452295738f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:19:20.092 | INFO     | c5cb85bf7f444622ae06be452295738f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 680.510ms
2025-09-10 03:24:19.411 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | 成功认证Java用户: admin
2025-09-10 03:24:20.067 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:24:20.069 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 660.694ms
2025-09-10 03:29:19.411 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | 成功认证Java用户: admin
2025-09-10 03:29:20.063 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:29:20.064 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.521ms
2025-09-10 03:34:19.408 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | 成功认证Java用户: admin
2025-09-10 03:34:20.095 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:34:20.098 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.569ms
2025-09-10 03:39:19.402 | INFO     | e25deb2467ef41649616f9d6db4918a9 | 成功认证Java用户: admin
2025-09-10 03:39:20.075 | INFO     | e25deb2467ef41649616f9d6db4918a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:39:20.076 | INFO     | e25deb2467ef41649616f9d6db4918a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 676.234ms
2025-09-10 03:44:19.413 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | 成功认证Java用户: admin
2025-09-10 03:44:20.068 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:44:20.069 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.233ms
2025-09-10 03:49:19.401 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | 成功认证Java用户: admin
2025-09-10 03:49:20.052 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:49:20.053 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.019ms
2025-09-10 03:54:19.408 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | 成功认证Java用户: admin
2025-09-10 03:54:20.113 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:54:20.115 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 709.353ms
2025-09-10 03:59:19.413 | INFO     | 1be8f72555654464a39140abab8ee1c1 | 成功认证Java用户: admin
2025-09-10 03:59:20.057 | INFO     | 1be8f72555654464a39140abab8ee1c1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:59:20.059 | INFO     | 1be8f72555654464a39140abab8ee1c1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 648.736ms
2025-09-10 04:04:19.423 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | 成功认证Java用户: admin
2025-09-10 04:04:20.085 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:04:20.086 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.403ms
2025-09-10 04:09:19.411 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | 成功认证Java用户: admin
2025-09-10 04:09:20.095 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:09:20.098 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.356ms
2025-09-10 04:14:19.415 | INFO     | f6cf008ee2544b5088507b8502183ed2 | 成功认证Java用户: admin
2025-09-10 04:14:20.059 | INFO     | f6cf008ee2544b5088507b8502183ed2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:14:20.063 | INFO     | f6cf008ee2544b5088507b8502183ed2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.485ms
2025-09-10 04:19:19.412 | INFO     | c3603dcc4e4a46f29096319956142bd3 | 成功认证Java用户: admin
2025-09-10 04:19:20.057 | INFO     | c3603dcc4e4a46f29096319956142bd3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:19:20.060 | INFO     | c3603dcc4e4a46f29096319956142bd3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.620ms
2025-09-10 04:24:19.409 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | 成功认证Java用户: admin
2025-09-10 04:24:20.051 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:24:20.054 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 647.353ms
2025-09-10 04:29:19.425 | INFO     | bf92402fe3744941b7a116fe39890158 | 成功认证Java用户: admin
2025-09-10 04:29:20.088 | INFO     | bf92402fe3744941b7a116fe39890158 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:29:20.091 | INFO     | bf92402fe3744941b7a116fe39890158 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.599ms
2025-09-10 04:34:19.412 | INFO     | 6155643326e94781857bf33790369cef | 成功认证Java用户: admin
2025-09-10 04:34:20.053 | INFO     | 6155643326e94781857bf33790369cef | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:34:20.056 | INFO     | 6155643326e94781857bf33790369cef | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.573ms
2025-09-10 04:39:19.404 | INFO     | 6610a9a132304c23b76eecff80216488 | 成功认证Java用户: admin
2025-09-10 04:39:20.071 | INFO     | 6610a9a132304c23b76eecff80216488 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:39:20.072 | INFO     | 6610a9a132304c23b76eecff80216488 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 671.344ms
2025-09-10 04:44:19.409 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | 成功认证Java用户: admin
2025-09-10 04:44:20.056 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:44:20.059 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 651.794ms
2025-09-10 04:49:19.403 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | 成功认证Java用户: admin
2025-09-10 04:49:20.077 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:49:20.078 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 677.174ms
2025-09-10 04:54:19.414 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | 成功认证Java用户: admin
2025-09-10 04:54:20.043 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:54:20.046 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 633.806ms
2025-09-10 04:59:19.405 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | 成功认证Java用户: admin
2025-09-10 04:59:20.084 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:59:20.085 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.536ms
2025-09-10 05:04:19.406 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | 成功认证Java用户: admin
2025-09-10 05:04:20.077 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:04:20.078 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.269ms
2025-09-10 05:09:19.416 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | 成功认证Java用户: admin
2025-09-10 05:09:20.080 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:09:20.081 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.096ms
2025-09-10 05:14:19.400 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | 成功认证Java用户: admin
2025-09-10 05:14:20.063 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:14:20.065 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.590ms
2025-09-10 05:19:19.414 | INFO     | 8941af80fcb640f19b15380d838d36d9 | 成功认证Java用户: admin
2025-09-10 05:19:20.079 | INFO     | 8941af80fcb640f19b15380d838d36d9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:19:20.082 | INFO     | 8941af80fcb640f19b15380d838d36d9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.829ms
2025-09-10 05:24:19.416 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | 成功认证Java用户: admin
2025-09-10 05:24:20.067 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:24:20.070 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.993ms
2025-09-10 05:29:19.404 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | 成功认证Java用户: admin
2025-09-10 05:29:20.067 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:29:20.070 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.443ms
2025-09-10 05:34:19.407 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | 成功认证Java用户: admin
2025-09-10 05:34:20.070 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:34:20.073 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.810ms
2025-09-10 05:39:19.419 | INFO     | 6a595cc0b44c406594450137df34a1c4 | 成功认证Java用户: admin
2025-09-10 05:39:20.076 | INFO     | 6a595cc0b44c406594450137df34a1c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:39:20.079 | INFO     | 6a595cc0b44c406594450137df34a1c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.966ms
2025-09-10 05:44:19.411 | INFO     | 274b59fcae554c4ab055eed854eea84f | 成功认证Java用户: admin
2025-09-10 05:44:20.066 | INFO     | 274b59fcae554c4ab055eed854eea84f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:44:20.068 | INFO     | 274b59fcae554c4ab055eed854eea84f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.761ms
2025-09-10 05:49:19.422 | INFO     | f195f670603e4e01b3090354b43d7d32 | 成功认证Java用户: admin
2025-09-10 05:49:20.088 | INFO     | f195f670603e4e01b3090354b43d7d32 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:49:20.089 | INFO     | f195f670603e4e01b3090354b43d7d32 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.456ms
2025-09-10 05:54:19.407 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | 成功认证Java用户: admin
2025-09-10 05:54:20.059 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:54:20.060 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 656.275ms
2025-09-10 05:59:19.404 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | 成功认证Java用户: admin
2025-09-10 05:59:20.060 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:59:20.062 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.527ms
2025-09-10 06:04:19.427 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | 成功认证Java用户: admin
2025-09-10 06:04:20.067 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:04:20.070 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.323ms
2025-09-10 06:09:19.408 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | 成功认证Java用户: admin
2025-09-10 06:09:20.084 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:09:20.087 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.787ms
2025-09-10 06:14:19.402 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | 成功认证Java用户: admin
2025-09-10 06:14:20.067 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:14:20.070 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.115ms
2025-09-10 06:19:19.416 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | 成功认证Java用户: admin
2025-09-10 06:19:20.095 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:19:20.097 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.248ms
2025-09-10 06:24:19.404 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | 成功认证Java用户: admin
2025-09-10 06:24:20.090 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:24:20.093 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 691.304ms
2025-09-10 06:29:19.401 | INFO     | 80393a7f17444b878875eff1d789152c | 成功认证Java用户: admin
2025-09-10 06:29:20.080 | INFO     | 80393a7f17444b878875eff1d789152c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:29:20.082 | INFO     | 80393a7f17444b878875eff1d789152c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.345ms
2025-09-10 06:34:19.405 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | 成功认证Java用户: admin
2025-09-10 06:34:20.045 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:34:20.047 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 643.951ms
2025-09-10 06:39:19.408 | INFO     | bc567bb4eed34a018aca7caf429510a9 | 成功认证Java用户: admin
2025-09-10 06:39:20.078 | INFO     | bc567bb4eed34a018aca7caf429510a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:39:20.080 | INFO     | bc567bb4eed34a018aca7caf429510a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.070ms
2025-09-10 06:44:19.421 | INFO     | 0b77fc953994407692785a70b97e10ff | 成功认证Java用户: admin
2025-09-10 06:44:20.060 | INFO     | 0b77fc953994407692785a70b97e10ff | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:44:20.063 | INFO     | 0b77fc953994407692785a70b97e10ff | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 644.800ms
2025-09-10 06:49:19.405 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | 成功认证Java用户: admin
2025-09-10 06:49:20.054 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:49:20.056 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 653.115ms
2025-09-10 06:54:19.408 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | 成功认证Java用户: admin
2025-09-10 06:54:20.055 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:54:20.056 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.577ms
2025-09-10 06:59:19.410 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | 成功认证Java用户: admin
2025-09-10 06:59:20.060 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:59:20.061 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 653.633ms
2025-09-10 07:04:19.403 | INFO     | 25c2502bcaef4a6695425b043f858a3d | 成功认证Java用户: admin
2025-09-10 07:04:20.054 | INFO     | 25c2502bcaef4a6695425b043f858a3d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:04:20.056 | INFO     | 25c2502bcaef4a6695425b043f858a3d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.772ms
2025-09-10 07:09:19.415 | INFO     | a664fec967894c93b668fae97c3bd9fb | 成功认证Java用户: admin
2025-09-10 07:09:20.081 | INFO     | a664fec967894c93b668fae97c3bd9fb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:09:20.084 | INFO     | a664fec967894c93b668fae97c3bd9fb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 671.521ms
2025-09-10 07:14:19.405 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | 成功认证Java用户: admin
2025-09-10 07:14:20.068 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:14:20.071 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.998ms
2025-09-10 07:19:19.409 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | 成功认证Java用户: admin
2025-09-10 07:19:20.074 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:19:20.076 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.588ms
2025-09-10 07:24:19.425 | INFO     | 5baf82e185994ef299dbdee07f8beeea | 成功认证Java用户: admin
2025-09-10 07:24:20.078 | INFO     | 5baf82e185994ef299dbdee07f8beeea | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:24:20.081 | INFO     | 5baf82e185994ef299dbdee07f8beeea | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.794ms
2025-09-10 07:29:19.400 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | 成功认证Java用户: admin
2025-09-10 07:29:20.068 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:29:20.071 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 673.246ms
2025-09-10 07:34:19.420 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | 成功认证Java用户: admin
2025-09-10 07:34:20.077 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:34:20.079 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.672ms
2025-09-10 07:39:19.412 | INFO     | cde1176f3af1494caf83ec7c150d4474 | 成功认证Java用户: admin
2025-09-10 07:39:20.076 | INFO     | cde1176f3af1494caf83ec7c150d4474 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:39:20.078 | INFO     | cde1176f3af1494caf83ec7c150d4474 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.398ms
2025-09-10 07:44:19.425 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | 成功认证Java用户: admin
2025-09-10 07:44:20.064 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:44:20.066 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 643.874ms
2025-09-10 07:49:19.406 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | 成功认证Java用户: admin
2025-09-10 07:49:20.048 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:49:20.050 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 646.292ms
2025-09-10 07:54:19.406 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | 成功认证Java用户: admin
2025-09-10 07:54:20.083 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:54:20.085 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 681.345ms
2025-09-10 07:59:19.411 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | 成功认证Java用户: admin
2025-09-10 07:59:20.079 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:59:20.090 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 681.745ms
2025-09-10 08:04:19.410 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | 成功认证Java用户: admin
2025-09-10 08:04:20.051 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:04:20.054 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.673ms
2025-09-10 08:09:19.411 | INFO     | 17db579f874c456b84a445c49aaf1feb | 成功认证Java用户: admin
2025-09-10 08:09:20.099 | INFO     | 17db579f874c456b84a445c49aaf1feb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:09:20.101 | INFO     | 17db579f874c456b84a445c49aaf1feb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 691.986ms
2025-09-10 08:14:19.407 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | 成功认证Java用户: admin
2025-09-10 08:14:20.157 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:14:20.161 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 756.053ms
2025-09-10 08:19:19.433 | INFO     | 0b7069b3061643329b0254f29b07794a | 成功认证Java用户: admin
2025-09-10 08:19:20.128 | INFO     | 0b7069b3061643329b0254f29b07794a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:19:20.130 | INFO     | 0b7069b3061643329b0254f29b07794a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 707.191ms
2025-09-10 08:24:19.404 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | 成功认证Java用户: admin
2025-09-10 08:24:20.082 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:24:20.086 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.855ms
2025-09-10 08:29:19.419 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | 成功认证Java用户: admin
2025-09-10 08:29:20.103 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:29:20.106 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.354ms
2025-09-10 08:44:32.326 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | 成功认证Java用户: admin
2025-09-10 08:44:33.004 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:44:33.008 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.883ms
2025-09-10 08:49:33.407 | INFO     | 448511cce9094534a8418fce7bdf901e | 成功认证Java用户: admin
2025-09-10 08:49:34.067 | INFO     | 448511cce9094534a8418fce7bdf901e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:49:34.071 | INFO     | 448511cce9094534a8418fce7bdf901e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.312ms
2025-09-10 08:54:33.406 | INFO     | abfdea747510416e83be5496bcf1b6c4 | 成功认证Java用户: admin
2025-09-10 08:54:34.057 | INFO     | abfdea747510416e83be5496bcf1b6c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:54:34.060 | INFO     | abfdea747510416e83be5496bcf1b6c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.456ms
2025-09-10 09:48:18.297 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:54:50.269 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:55:29.641 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:58:20.066 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:02:36.143 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:03:10.380 | INFO     | 54cab4dcb1544a1b953f6fa50af2129a | 127.0.0.1       | GET      | 200    | /docs | 2.821ms
2025-09-10 10:03:12.483 | INFO     | 7f76e31bbbd040698c62a4bea0998758 | 127.0.0.1       | GET      | 200    | /openapi | 87.193ms
2025-09-10 10:11:17.789 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | 成功认证Java用户: admin
2025-09-10 10:11:17.792 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 成功认证Java用户: admin
2025-09-10 10:11:17.793 | INFO     | afca969c83a34464b757328d9b36fac9 | 成功认证Java用户: admin
2025-09-10 10:11:17.837 | INFO     | afca969c83a34464b757328d9b36fac9 | 权限检查通过: user_id=1, permission=knowledge:base:list, path=/api/iot/v1/knowledge-base/list
2025-09-10 10:11:17.838 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 权限检查通过: user_id=1, permission=knowledge:base:stats, path=/api/iot/v1/knowledge-base/stats/overview
2025-09-10 10:11:18.138 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-09-10 10:11:18.140 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/health | 367.272ms
2025-09-10 10:11:18.142 | INFO     | afca969c83a34464b757328d9b36fac9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:18.144 | INFO     | e075194dca4c4ec9980f3efb43441f68 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:18.145 | INFO     | afca969c83a34464b757328d9b36fac9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 363.225ms
2025-09-10 10:11:18.145 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 363.829ms
2025-09-10 10:11:23.447 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | 成功认证Java用户: admin
2025-09-10 10:11:23.448 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 成功认证Java用户: admin
2025-09-10 10:11:23.449 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 成功认证Java用户: admin
2025-09-10 10:11:23.461 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 权限检查通过: user_id=1, permission=knowledge:base:stats, path=/api/iot/v1/knowledge-base/stats/overview
2025-09-10 10:11:23.462 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 权限检查通过: user_id=1, permission=knowledge:base:list, path=/api/iot/v1/knowledge-base/list
2025-09-10 10:11:23.480 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-09-10 10:11:23.482 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:23.483 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:23.483 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/health | 37.713ms
2025-09-10 10:11:23.484 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.942ms
2025-09-10 10:11:23.484 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 38.576ms
2025-09-10 10:11:27.100 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | 成功认证Java用户: admin
2025-09-10 10:11:27.495 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:11:27.497 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.851ms
2025-09-10 10:15:31.643 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 成功认证Java用户: admin
2025-09-10 10:15:31.652 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:15:32.338 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 698.021ms
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | AI服务响应状态码: 200
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 开始处理流式响应数据
2025-09-10 10:15:35.483 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 流式响应结束，原因: stop
2025-09-10 10:15:35.483 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 流式响应完成，共处理 101 个数据块，总长度: 176
2025-09-10 10:16:28.403 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | 成功认证Java用户: admin
2025-09-10 10:16:28.818 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:16:28.820 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 419.646ms
2025-09-10 10:21:28.406 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | 成功认证Java用户: admin
2025-09-10 10:21:28.791 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:21:28.792 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.464ms
2025-09-10 10:21:37.258 | INFO     | 29594f50e29e47ec81635911192332eb | 成功认证Java用户: admin
2025-09-10 10:21:37.637 | INFO     | 29594f50e29e47ec81635911192332eb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:21:37.639 | INFO     | 29594f50e29e47ec81635911192332eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 382.358ms
2025-09-10 10:22:00.445 | INFO     | 6d119c9a85f249ed947d64297c75435d | 成功认证Java用户: admin
2025-09-10 10:22:00.836 | INFO     | 6d119c9a85f249ed947d64297c75435d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:22:00.837 | INFO     | 6d119c9a85f249ed947d64297c75435d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.879ms
2025-09-10 10:22:24.123 | INFO     | 012f83e9db004224884de2ee0c148a1d | 成功认证Java用户: admin
2025-09-10 10:22:24.522 | INFO     | 012f83e9db004224884de2ee0c148a1d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:22:24.523 | INFO     | 012f83e9db004224884de2ee0c148a1d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.543ms
2025-09-10 10:24:22.110 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | 成功认证Java用户: admin
2025-09-10 10:24:22.498 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:24:22.500 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.146ms
2025-09-10 10:25:39.160 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | 成功认证Java用户: admin
2025-09-10 10:25:39.690 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:25:39.694 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 536.676ms
2025-09-10 10:26:11.092 | INFO     | 849af37fccd0409da50141e91db9a4b6 | 成功认证Java用户: admin
2025-09-10 10:26:11.531 | INFO     | 849af37fccd0409da50141e91db9a4b6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:11.533 | INFO     | 849af37fccd0409da50141e91db9a4b6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 443.296ms
2025-09-10 10:26:28.409 | INFO     | 557381ca4fa048b79237c6bce915f504 | 成功认证Java用户: admin
2025-09-10 10:26:28.822 | INFO     | 557381ca4fa048b79237c6bce915f504 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:28.824 | INFO     | 557381ca4fa048b79237c6bce915f504 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 415.942ms
2025-09-10 10:26:38.405 | INFO     | 280593b69781492db025e0ea3c8d4f6d | 成功认证Java用户: admin
2025-09-10 10:26:38.785 | INFO     | 280593b69781492db025e0ea3c8d4f6d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:38.787 | INFO     | 280593b69781492db025e0ea3c8d4f6d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.415ms
2025-09-10 10:27:01.413 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | 成功认证Java用户: admin
2025-09-10 10:27:01.817 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:27:01.819 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 407.146ms
2025-09-10 10:27:25.402 | INFO     | 77ff3e648b9a40319feda1911005d973 | 成功认证Java用户: admin
2025-09-10 10:27:25.828 | INFO     | 77ff3e648b9a40319feda1911005d973 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:27:25.830 | INFO     | 77ff3e648b9a40319feda1911005d973 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 429.653ms
2025-09-10 10:28:07.963 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:28:59.494 | INFO     | 55abc599bcf5426d9cff46bea73af500 | 127.0.0.1       | POST     | 401    | /api/iot/v1/chat/chat | 0.474ms
2025-09-10 10:29:09.965 | INFO     | d4f879f2c7a341ae87d8dc38e7766250 | 127.0.0.1       | GET      | 200    | /docs | 2.346ms
2025-09-10 10:29:10.064 | INFO     | 5df8c5b19522400e911ada53126422d6 | 127.0.0.1       | GET      | 200    | /openapi | 4.718ms
2025-09-10 10:29:22.512 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | 成功认证Java用户: admin
2025-09-10 10:29:23.211 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:29:23.214 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 703.316ms
2025-09-10 10:29:25.010 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 成功认证Java用户: admin
2025-09-10 10:29:25.013 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:29:25.700 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 691.262ms
2025-09-10 10:29:25.713 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:29:25.713 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | AI服务响应状态码: 200
2025-09-10 10:29:25.714 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 开始处理流式响应数据
2025-09-10 10:29:45.568 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 流式响应结束，原因: stop
2025-09-10 10:29:45.569 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 流式响应完成，共处理 613 个数据块，总长度: 1070
2025-09-10 10:29:57.204 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 成功认证Java用户: admin
2025-09-10 10:29:57.206 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 已清空用户 1 的会话 chat_session_1757471171063_cddth1gb77 历史记录
2025-09-10 10:29:57.206 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471171063_cddth1gb77 | 4.033ms
2025-09-10 10:30:14.098 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 成功认证Java用户: admin
2025-09-10 10:30:14.102 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:30:14.778 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 681.443ms
2025-09-10 10:30:14.790 | INFO     | 98c7af478d0744b0be6faa024f7de199 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:30:14.790 | INFO     | 98c7af478d0744b0be6faa024f7de199 | AI服务响应状态码: 200
2025-09-10 10:30:14.791 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 开始处理流式响应数据
2025-09-10 10:30:26.822 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 流式响应结束，原因: stop
2025-09-10 10:30:26.822 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 流式响应完成，共处理 374 个数据块，总长度: 634
2025-09-10 10:30:30.105 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 成功认证Java用户: admin
2025-09-10 10:30:30.107 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 已清空用户 1 的会话 chat_session_1757471397212_0z782q7xtjw 历史记录
2025-09-10 10:30:30.107 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471397212_0z782q7xtjw | 3.757ms
2025-09-10 10:30:39.706 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | 成功认证Java用户: admin
2025-09-10 10:30:40.413 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:30:40.416 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 711.462ms
2025-09-10 10:30:42.120 | INFO     | d08948cd502f4207b0266371925005e9 | 成功认证Java用户: admin
2025-09-10 10:30:42.123 | INFO     | d08948cd502f4207b0266371925005e9 | 已清空用户 1 的会话 chat_session_1757471430116_l6ms115j60s 历史记录
2025-09-10 10:30:42.123 | INFO     | d08948cd502f4207b0266371925005e9 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471430116_l6ms115j60s | 4.080ms
2025-09-10 10:31:12.413 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | 成功认证Java用户: admin
2025-09-10 10:31:12.789 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:12.791 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 380.151ms
2025-09-10 10:31:28.403 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | 成功认证Java用户: admin
2025-09-10 10:31:28.851 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:28.853 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 451.046ms
2025-09-10 10:31:38.414 | INFO     | cab4ade0b44f438cafb1399f88852b1a | 成功认证Java用户: admin
2025-09-10 10:31:38.803 | INFO     | cab4ade0b44f438cafb1399f88852b1a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:38.804 | INFO     | cab4ade0b44f438cafb1399f88852b1a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 391.319ms
2025-09-10 10:31:41.575 | INFO     | e2f83b41a49342a3a8dcf5a6c656b144 | 成功认证Java用户: admin
2025-09-10 10:31:41.578 | INFO     | e2f83b41a49342a3a8dcf5a6c656b144 | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 3.267ms
2025-09-10 10:32:00.497 | INFO     | 6fc4f4e8548c4be8be57553936fc8abe | 成功认证Java用户: admin
2025-09-10 10:32:00.500 | INFO     | 6fc4f4e8548c4be8be57553936fc8abe | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 4.430ms
2025-09-10 10:32:01.410 | INFO     | e51ebac5e5e4463f84dd01032c24170c | 成功认证Java用户: admin
2025-09-10 10:32:01.793 | INFO     | e51ebac5e5e4463f84dd01032c24170c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:32:01.794 | INFO     | e51ebac5e5e4463f84dd01032c24170c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.411ms
2025-09-10 10:32:25.398 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | 成功认证Java用户: admin
2025-09-10 10:32:25.790 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:32:25.792 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 394.945ms
2025-09-10 10:34:23.411 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | 成功认证Java用户: admin
2025-09-10 10:34:23.809 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:34:23.811 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.539ms
2025-09-10 10:35:40.408 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | 成功认证Java用户: admin
2025-09-10 10:35:40.792 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:35:40.796 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 391.047ms
2025-09-10 10:36:11.549 | INFO     | c096f68bb48f41afa97506063f1d26b1 | 成功认证Java用户: admin
2025-09-10 10:36:11.923 | INFO     | c096f68bb48f41afa97506063f1d26b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:11.925 | INFO     | c096f68bb48f41afa97506063f1d26b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 377.818ms
2025-09-10 10:36:27.512 | INFO     | ee039be8649841fb96d109ec253b5fc5 | 成功认证Java用户: admin
2025-09-10 10:36:28.217 | INFO     | ee039be8649841fb96d109ec253b5fc5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:28.221 | INFO     | ee039be8649841fb96d109ec253b5fc5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 710.509ms
2025-09-10 10:36:38.402 | INFO     | 8f740b0f52b846018784beefb37dc47e | 成功认证Java用户: admin
2025-09-10 10:36:38.784 | INFO     | 8f740b0f52b846018784beefb37dc47e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:38.786 | INFO     | 8f740b0f52b846018784beefb37dc47e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.067ms
2025-09-10 10:37:01.404 | INFO     | 5a2c54042c244562ab7da5736b49143e | 成功认证Java用户: admin
2025-09-10 10:37:01.795 | INFO     | 5a2c54042c244562ab7da5736b49143e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:37:01.797 | INFO     | 5a2c54042c244562ab7da5736b49143e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 393.923ms
2025-09-10 10:37:25.412 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | 成功认证Java用户: admin
2025-09-10 10:37:25.946 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:37:25.950 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 539.353ms
2025-09-10 10:39:23.411 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | 成功认证Java用户: admin
2025-09-10 10:39:23.791 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:39:23.793 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.360ms
2025-09-10 10:39:45.263 | INFO     | 88abaef1d3a148f1be2966903f4cb908 | 成功认证Java用户: admin
2025-09-10 10:39:45.264 | INFO     | 88abaef1d3a148f1be2966903f4cb908 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 3.352ms
2025-09-10 10:39:47.294 | INFO     | 54984ac4737b4e5e974b3233587ed7ed | 成功认证Java用户: admin
2025-09-10 10:39:47.299 | INFO     | 54984ac4737b4e5e974b3233587ed7ed | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 6.129ms
2025-09-10 10:39:49.345 | INFO     | ec9c50299ca6469b96fc3fb56391cae9 | 成功认证Java用户: admin
2025-09-10 10:39:49.347 | INFO     | ec9c50299ca6469b96fc3fb56391cae9 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.397ms
2025-09-10 10:39:51.386 | INFO     | c5932a72aeca4effafab42c84d9c4f88 | 成功认证Java用户: admin
2025-09-10 10:39:51.389 | INFO     | c5932a72aeca4effafab42c84d9c4f88 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.795ms
2025-09-10 10:40:40.409 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | 成功认证Java用户: admin
2025-09-10 10:40:40.815 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:40:40.818 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 410.478ms
2025-09-10 10:41:12.410 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | 成功认证Java用户: admin
2025-09-10 10:41:12.790 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:12.792 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.167ms
2025-09-10 10:41:28.407 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | 成功认证Java用户: admin
2025-09-10 10:41:28.792 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:28.794 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.320ms
2025-09-10 10:41:38.408 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | 成功认证Java用户: admin
2025-09-10 10:41:38.791 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:38.794 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.147ms
2025-09-10 10:42:01.409 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | 成功认证Java用户: admin
2025-09-10 10:42:01.813 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:42:01.815 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.122ms
2025-09-10 10:42:25.401 | INFO     | afa7c200843846c98313f8533a18b793 | 成功认证Java用户: admin
2025-09-10 10:42:25.793 | INFO     | afa7c200843846c98313f8533a18b793 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:42:25.794 | INFO     | afa7c200843846c98313f8533a18b793 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 395.603ms
2025-09-10 10:44:23.406 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | 成功认证Java用户: admin
2025-09-10 10:44:23.781 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:44:23.783 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.334ms
2025-09-10 10:45:40.409 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | 成功认证Java用户: admin
2025-09-10 10:45:40.811 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:45:40.813 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 405.420ms
2025-09-10 10:46:12.410 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | 成功认证Java用户: admin
2025-09-10 10:46:12.786 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:46:12.788 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.563ms
2025-09-10 10:46:24.851 | INFO     | ebb8169c0f0e4b74a72758adae277789 | 成功认证Java用户: admin
2025-09-10 10:46:24.853 | INFO     | ebb8169c0f0e4b74a72758adae277789 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 4.242ms
2025-09-10 10:46:26.886 | INFO     | 3542dc0775f0452f8450d38384460119 | 成功认证Java用户: admin
2025-09-10 10:46:26.889 | INFO     | 3542dc0775f0452f8450d38384460119 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 5.119ms
2025-09-10 10:46:28.938 | INFO     | cca27205719846f48c5866fdeda3fe24 | 成功认证Java用户: admin
2025-09-10 10:46:28.940 | INFO     | cca27205719846f48c5866fdeda3fe24 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.572ms
2025-09-10 10:46:30.995 | INFO     | c88ab5ce1cf04300b201f9d184f4fb25 | 成功认证Java用户: admin
2025-09-10 10:46:30.998 | INFO     | c88ab5ce1cf04300b201f9d184f4fb25 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 5.862ms
2025-09-10 10:46:38.405 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | 成功认证Java用户: admin
2025-09-10 10:46:38.780 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:46:38.782 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 378.422ms
2025-09-10 10:47:06.226 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:47:19.423 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | 成功认证Java用户: admin
2025-09-10 10:47:19.839 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:47:19.841 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 424.095ms
2025-09-10 10:47:22.669 | INFO     | 2d64046e78ce4b0db580b92828062448 | 成功认证Java用户: admin
2025-09-10 10:47:22.671 | INFO     | 2d64046e78ce4b0db580b92828062448 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 4.174ms
2025-09-10 10:47:24.733 | INFO     | b24898462efa4663a51e2bba3ffd369f | 成功认证Java用户: admin
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 初始化LangGraph智能体服务...
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 加载了 3 个工具
2025-09-10 10:47:24.741 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:47:24.741 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体服务初始化完成
2025-09-10 10:47:25.613 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | 成功认证Java用户: admin
2025-09-10 10:47:25.993 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:47:25.995 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.247ms
2025-09-10 10:47:26.006 | INFO     | b24898462efa4663a51e2bba3ffd369f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:47:26.007 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体执行完成: 5685b14b-6c7f-4b04-8a25-821ea40c310b
2025-09-10 10:47:26.017 | INFO     | b24898462efa4663a51e2bba3ffd369f | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 1284.468ms
2025-09-10 10:47:28.049 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 成功认证Java用户: admin
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 用户 1 发起统一聊天: 查询Vue.js的生命周期钩子函数... (模式: agent)
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 初始化LangGraph智能体服务...
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 加载了 3 个工具
2025-09-10 10:47:28.053 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:47:28.053 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体服务初始化完成
2025-09-10 10:47:28.072 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:47:28.074 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体执行完成: 39b1544b-388c-4674-9e36-8f5c277d74b2
2025-09-10 10:47:28.074 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 27.318ms
2025-09-10 10:47:30.119 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 成功认证Java用户: admin
2025-09-10 10:47:30.120 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 用户 1 发起统一聊天: 你好，请介绍一下你的功能... (模式: auto)
2025-09-10 10:47:30.500 | INFO     | 1661401f64cd43049f4e169322fdf4aa | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:47:30.888 | INFO     | 1661401f64cd43049f4e169322fdf4aa | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:47:30.890 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 773.083ms
2025-09-10 10:49:23.399 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | 成功认证Java用户: admin
2025-09-10 10:49:23.820 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:49:23.821 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 424.570ms
2025-09-10 10:50:12.261 | INFO     | beb0beb3ea094aef97f51d404b371e36 | 成功认证Java用户: admin
2025-09-10 10:50:12.263 | INFO     | beb0beb3ea094aef97f51d404b371e36 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 3.595ms
2025-09-10 10:50:14.310 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 成功认证Java用户: admin
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 初始化LangGraph智能体服务...
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 加载了 3 个工具
2025-09-10 10:50:14.316 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:50:14.316 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体服务初始化完成
2025-09-10 10:50:14.336 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:50:14.337 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体执行完成: d0591fa8-9d21-4530-8aa3-c77ba8f1c07d
2025-09-10 10:50:14.338 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 30.049ms
2025-09-10 10:50:16.405 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 成功认证Java用户: admin
2025-09-10 10:50:16.407 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 用户 1 发起统一聊天: 查询Vue.js的生命周期钩子函数... (模式: agent)
2025-09-10 10:50:16.407 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 初始化LangGraph智能体服务...
2025-09-10 10:50:16.408 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 加载了 3 个工具
2025-09-10 10:50:16.409 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:50:16.410 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体服务初始化完成
2025-09-10 10:50:16.428 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:50:16.429 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体执行完成: a3a7925a-7d24-43f8-8fb5-0e34b9f46d74
2025-09-10 10:50:16.430 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 26.494ms
2025-09-10 10:50:18.476 | INFO     | 523576b059764124bd01b2cec9b3745e | 成功认证Java用户: admin
2025-09-10 10:50:18.478 | INFO     | 523576b059764124bd01b2cec9b3745e | 用户 1 发起统一聊天: 你好，请介绍一下你的功能... (模式: auto)
2025-09-10 10:50:18.859 | INFO     | 523576b059764124bd01b2cec9b3745e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:50:19.241 | INFO     | 523576b059764124bd01b2cec9b3745e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:50:19.243 | INFO     | 523576b059764124bd01b2cec9b3745e | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 768.580ms
2025-09-10 10:50:40.404 | INFO     | b196c81597934adead3adfcd6d4eed17 | 成功认证Java用户: admin
2025-09-10 10:50:40.784 | INFO     | b196c81597934adead3adfcd6d4eed17 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:50:40.786 | INFO     | b196c81597934adead3adfcd6d4eed17 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.246ms
2025-09-10 10:53:02.558 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:53:27.378 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 成功认证Java用户: admin
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 初始化LangGraph智能体服务...
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 加载了 3 个工具
2025-09-10 10:53:27.385 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:53:27.386 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体服务初始化完成
2025-09-10 10:53:28.323 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:53:28.325 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体执行完成: b8acd284-dc9e-4e82-9338-d44dd13d61c5
2025-09-10 10:53:28.327 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 955.879ms
2025-09-10 10:54:23.408 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | 成功认证Java用户: admin
2025-09-10 10:54:23.785 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:54:23.787 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 380.816ms
2025-09-10 10:54:29.653 | INFO     | 04509ae667754c68b310906b51c3f9fb | 成功认证Java用户: admin
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 初始化LangGraph智能体服务...
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 加载了 3 个工具
2025-09-10 10:54:29.658 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:54:29.658 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体服务初始化完成
2025-09-10 10:54:29.678 | INFO     | 04509ae667754c68b310906b51c3f9fb | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:54:29.681 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体执行完成: cd079743-f029-40de-a8d3-c87078aa7aaa
2025-09-10 10:54:29.682 | INFO     | 04509ae667754c68b310906b51c3f9fb | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 30.051ms
2025-09-10 10:55:07.753 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:55:40.416 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | 成功认证Java用户: admin
2025-09-10 10:55:40.815 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:55:40.817 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.928ms
2025-09-10 10:56:12.404 | INFO     | f6003f10daa84d459f80180e8bbf6240 | 成功认证Java用户: admin
2025-09-10 10:56:12.782 | INFO     | f6003f10daa84d459f80180e8bbf6240 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:12.785 | INFO     | f6003f10daa84d459f80180e8bbf6240 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.402ms
2025-09-10 10:56:43.278 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | 成功认证Java用户: admin
2025-09-10 10:56:43.682 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:43.684 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.400ms
2025-09-10 10:56:43.688 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | 成功认证Java用户: admin
2025-09-10 10:56:44.089 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:44.091 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 405.476ms
2025-09-10 10:56:44.352 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 成功认证Java用户: admin
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 初始化LangGraph智能体服务...
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 加载了 3 个工具
2025-09-10 10:56:44.359 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:56:44.360 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph智能体服务初始化完成
2025-09-10 10:56:45.304 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:56:45.306 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph智能体执行完成: c3ba3b95-7ae5-4374-8c98-e9359ad8dc6c
2025-09-10 10:56:45.308 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 956.929ms
2025-09-10 10:57:01.404 | INFO     | 735b3cd05f7844d8a637679e2d801626 | 成功认证Java用户: admin
2025-09-10 10:57:02.128 | INFO     | 735b3cd05f7844d8a637679e2d801626 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:57:02.131 | INFO     | 735b3cd05f7844d8a637679e2d801626 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 729.562ms
2025-09-10 10:57:25.406 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | 成功认证Java用户: admin
2025-09-10 10:57:25.795 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:57:25.797 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.046ms
2025-09-10 10:58:28.652 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:59:46.546 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 成功认证Java用户: admin
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 初始化LangGraph智能体服务...
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 加载了 3 个工具
2025-09-10 10:59:46.553 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:59:46.553 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph智能体服务初始化完成
2025-09-10 10:59:51.931 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:59:57.015 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:59:57.017 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph智能体执行完成: ce02a334-4166-4f3d-b474-fa6f5ff1d413
2025-09-10 10:59:57.018 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 10479.854ms
2025-09-10 11:00:19.412 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | 成功认证Java用户: admin
2025-09-10 11:00:19.793 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:00:19.795 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 385.824ms
2025-09-10 11:00:39.707 | INFO     | 5a22a16a95554163918cc4df697ff329 | 成功认证Java用户: admin
2025-09-10 11:00:40.413 | INFO     | 5a22a16a95554163918cc4df697ff329 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:00:40.415 | INFO     | 5a22a16a95554163918cc4df697ff329 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 710.837ms
2025-09-10 11:00:47.347 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 成功认证Java用户: admin
2025-09-10 11:00:47.354 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:00:48.053 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 707.942ms
2025-09-10 11:00:48.064 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:00:48.064 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | AI服务响应状态码: 200
2025-09-10 11:00:48.066 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 开始处理流式响应数据
2025-09-10 11:00:55.083 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 流式响应结束，原因: stop
2025-09-10 11:00:55.084 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 流式响应完成，共处理 220 个数据块，总长度: 388
2025-09-10 11:01:12.412 | INFO     | 8bef79c032de4c2192a3f4c354748392 | 成功认证Java用户: admin
2025-09-10 11:01:12.786 | INFO     | 8bef79c032de4c2192a3f4c354748392 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:12.788 | INFO     | 8bef79c032de4c2192a3f4c354748392 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 376.497ms
2025-09-10 11:01:28.409 | INFO     | ad29500958e642d191c80dd7469a9a09 | 成功认证Java用户: admin
2025-09-10 11:01:28.798 | INFO     | ad29500958e642d191c80dd7469a9a09 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:28.800 | INFO     | ad29500958e642d191c80dd7469a9a09 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.115ms
2025-09-10 11:01:38.402 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | 成功认证Java用户: admin
2025-09-10 11:01:38.788 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:38.789 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.852ms
2025-09-10 11:02:01.404 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | 成功认证Java用户: admin
2025-09-10 11:02:01.789 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:02:01.790 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 387.356ms
2025-09-10 11:02:44.066 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | 成功认证Java用户: admin
2025-09-10 11:02:44.448 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:02:44.450 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.716ms
2025-09-10 11:05:19.412 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | 成功认证Java用户: admin
2025-09-10 11:05:19.825 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:05:19.827 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 417.860ms
2025-09-10 11:06:19.410 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | 成功认证Java用户: admin
2025-09-10 11:06:19.905 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:06:19.908 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 500.557ms
2025-09-10 11:06:19.911 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | 成功认证Java用户: admin
2025-09-10 11:06:20.318 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:06:20.321 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 412.158ms
2025-09-10 11:07:19.412 | INFO     | c669751569e742bea80cbb8dce5a41f4 | 成功认证Java用户: admin
2025-09-10 11:07:19.842 | INFO     | c669751569e742bea80cbb8dce5a41f4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:19.844 | INFO     | c669751569e742bea80cbb8dce5a41f4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 433.813ms
2025-09-10 11:07:19.848 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | 成功认证Java用户: admin
2025-09-10 11:07:20.253 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:20.254 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.798ms
2025-09-10 11:07:20.258 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | 成功认证Java用户: admin
2025-09-10 11:07:20.663 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:20.665 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.595ms
2025-09-10 11:08:19.402 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | 成功认证Java用户: admin
2025-09-10 11:08:19.784 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:08:19.786 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.538ms
2025-09-10 11:08:54.583 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:09:11.546 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 成功认证Java用户: admin
2025-09-10 11:09:11.550 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:09:11.551 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 初始化LangGraph智能体服务...
2025-09-10 11:09:11.551 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 加载了 3 个工具
2025-09-10 11:09:11.553 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:09:11.554 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph智能体服务初始化完成
2025-09-10 11:09:16.527 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:09:21.105 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:09:21.106 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph智能体执行完成: 145dbf10-007d-4691-80b3-128a4a1f57b7
2025-09-10 11:09:21.107 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 9567.661ms
2025-09-10 11:09:56.448 | INFO     | b8e544d5b4294ecfb368412c6c12dede | 成功认证Java用户: admin
2025-09-10 11:09:56.856 | INFO     | b8e544d5b4294ecfb368412c6c12dede | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:09:56.859 | INFO     | b8e544d5b4294ecfb368412c6c12dede | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 414.021ms
2025-09-10 11:09:58.371 | INFO     | bdedab717f7b446ea9c715442f706673 | 成功认证Java用户: admin
2025-09-10 11:09:59.051 | INFO     | bdedab717f7b446ea9c715442f706673 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:09:59.053 | INFO     | bdedab717f7b446ea9c715442f706673 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.752ms
2025-09-10 11:10:01.852 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 成功认证Java用户: admin
2025-09-10 11:10:01.859 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:02.560 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 708.634ms
2025-09-10 11:10:02.572 | INFO     | cff3e6ab434444d085b10d194d3fff42 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:02.572 | INFO     | cff3e6ab434444d085b10d194d3fff42 | AI服务响应状态码: 200
2025-09-10 11:10:02.573 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 开始处理流式响应数据
2025-09-10 11:10:07.377 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 流式响应结束，原因: stop
2025-09-10 11:10:07.377 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 流式响应完成，共处理 151 个数据块，总长度: 262
2025-09-10 11:10:16.515 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 成功认证Java用户: admin
2025-09-10 11:10:16.517 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 已清空用户 1 的会话 chat_session_1757473798343_gmrfsyn9qxn 历史记录
2025-09-10 11:10:16.517 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757473798343_gmrfsyn9qxn | 4.508ms
2025-09-10 11:10:20.503 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 成功认证Java用户: admin
2025-09-10 11:10:20.508 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:21.190 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 688.303ms
2025-09-10 11:10:21.202 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:21.202 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | AI服务响应状态码: 200
2025-09-10 11:10:21.203 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 开始处理流式响应数据
2025-09-10 11:10:29.052 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 流式响应结束，原因: stop
2025-09-10 11:10:29.053 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 流式响应完成，共处理 246 个数据块，总长度: 430
2025-09-10 11:10:38.103 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 成功认证Java用户: admin
2025-09-10 11:10:38.106 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:38.790 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 688.875ms
2025-09-10 11:10:38.802 | INFO     | a033bad5e31e4fe68e7d487182716b89 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:38.802 | INFO     | a033bad5e31e4fe68e7d487182716b89 | AI服务响应状态码: 200
2025-09-10 11:10:38.803 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 开始处理流式响应数据
2025-09-10 11:10:39.720 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | 成功认证Java用户: admin
2025-09-10 11:10:40.407 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:10:40.412 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 693.704ms
2025-09-10 11:10:48.527 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 流式响应结束，原因: stop
2025-09-10 11:10:48.528 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 流式响应完成，共处理 303 个数据块，总长度: 557
2025-09-10 11:11:11.568 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | 成功认证Java用户: admin
2025-09-10 11:11:11.987 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:11.989 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 423.506ms
2025-09-10 11:11:28.404 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | 成功认证Java用户: admin
2025-09-10 11:11:28.794 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:28.795 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.566ms
2025-09-10 11:11:38.398 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | 成功认证Java用户: admin
2025-09-10 11:11:38.792 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:38.794 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 397.430ms
2025-09-10 11:12:01.406 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | 成功认证Java用户: admin
2025-09-10 11:12:01.800 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:12:01.803 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.002ms
2025-09-10 11:13:19.412 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | 成功认证Java用户: admin
2025-09-10 11:13:19.823 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:13:19.825 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 416.403ms
2025-09-10 11:14:40.314 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:14:54.139 | INFO     | 492673091b954ec9b21d448328296317 | 成功认证Java用户: admin
2025-09-10 11:14:54.142 | INFO     | 492673091b954ec9b21d448328296317 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:14:54.143 | INFO     | 492673091b954ec9b21d448328296317 | 初始化LangGraph智能体服务...
2025-09-10 11:14:54.143 | INFO     | 492673091b954ec9b21d448328296317 | 加载了 3 个工具
2025-09-10 11:14:54.145 | INFO     | 492673091b954ec9b21d448328296317 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:14:54.146 | INFO     | 492673091b954ec9b21d448328296317 | LangGraph智能体服务初始化完成
2025-09-10 11:14:59.223 | INFO     | 492673091b954ec9b21d448328296317 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:14:59.408 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | 成功认证Java用户: admin
2025-09-10 11:14:59.791 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:14:59.794 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 387.008ms
2025-09-10 11:15:03.127 | INFO     | 492673091b954ec9b21d448328296317 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:15:03.130 | INFO     | 492673091b954ec9b21d448328296317 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 8997.819ms
2025-09-10 11:15:19.403 | INFO     | bffed3e660944219b6733348b77bcd9d | 成功认证Java用户: admin
2025-09-10 11:15:19.842 | INFO     | bffed3e660944219b6733348b77bcd9d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:15:19.844 | INFO     | bffed3e660944219b6733348b77bcd9d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 442.868ms
2025-09-10 11:17:56.201 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:18:19.420 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | 成功认证Java用户: admin
2025-09-10 11:18:19.842 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:18:19.843 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 430.488ms
2025-09-10 11:19:06.253 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:20:13.353 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:20:19.416 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | 成功认证Java用户: admin
2025-09-10 11:20:19.821 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:20:19.823 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 414.965ms
2025-09-10 11:21:07.219 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:21:19.423 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | 成功认证Java用户: admin
2025-09-10 11:21:19.832 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:21:19.834 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 418.324ms
2025-09-10 11:21:19.837 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | 成功认证Java用户: admin
2025-09-10 11:21:20.218 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:21:20.219 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.358ms
2025-09-10 11:21:26.313 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 成功认证Java用户: admin
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 初始化LangGraph智能体服务...
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 加载了 3 个工具
2025-09-10 11:21:26.320 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:21:26.320 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体服务初始化完成
2025-09-10 11:21:32.036 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:21:34.129 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-3665d30d7d784412a28dcd570ea39890', 'type': 'tool_call'}
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 提取的工具信息: name=name, args={'expression': '2 + 3'}, id=tool_1_0
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 成功添加工具调用信息: {'tool_name': 'name', 'tool_display_name': 'name', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:21:34.133580', 'end_time': '2025-09-10T11:21:34.133580', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'tool_1_0'}}
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我应该用计算器工具。先调用calculator函数，表达式是2+3。然后得到结果5，直接告诉用户答案就行了。不需要其他步骤，很简单。
</think>

2 
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体执行完成: 0752624d-2c85-49d0-bcf1-4b5d42759331
2025-09-10 11:21:34.135 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 7823.265ms
2025-09-10 11:22:19.407 | INFO     | fece041bf8af4e498f74123183b1e61b | 成功认证Java用户: admin
2025-09-10 11:22:19.825 | INFO     | fece041bf8af4e498f74123183b1e61b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:19.828 | INFO     | fece041bf8af4e498f74123183b1e61b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 422.695ms
2025-09-10 11:22:19.831 | INFO     | 51e0eb670b934a5381e480480c00641a | 成功认证Java用户: admin
2025-09-10 11:22:20.247 | INFO     | 51e0eb670b934a5381e480480c00641a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:20.249 | INFO     | 51e0eb670b934a5381e480480c00641a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 419.937ms
2025-09-10 11:22:20.251 | INFO     | e94870d7b8d648a48e19508b85ed9740 | 成功认证Java用户: admin
2025-09-10 11:22:20.695 | INFO     | e94870d7b8d648a48e19508b85ed9740 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:20.698 | INFO     | e94870d7b8d648a48e19508b85ed9740 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 447.483ms
2025-09-10 11:22:51.783 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | 成功认证Java用户: admin
2025-09-10 11:22:52.167 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:52.169 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.103ms
2025-09-10 11:24:25.720 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:24:33.224 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | 成功认证Java用户: admin
2025-09-10 11:24:33.659 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:24:33.661 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 445.008ms
2025-09-10 11:24:52.957 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 成功认证Java用户: admin
2025-09-10 11:24:52.961 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:24:52.961 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 初始化LangGraph智能体服务...
2025-09-10 11:24:52.962 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 加载了 3 个工具
2025-09-10 11:24:52.964 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:24:52.964 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph智能体服务初始化完成
2025-09-10 11:24:59.175 | INFO     | 3bb641b422e24f748d6203717aa8d026 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:24:59.406 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | 成功认证Java用户: admin
2025-09-10 11:24:59.812 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:24:59.814 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 409.597ms
2025-09-10 11:25:02.272 | INFO     | 3bb641b422e24f748d6203717aa8d026 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。因为这是一个基本的加法运算，没有涉及三角函数或对数等高
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712', 'type': 'tool_call'}
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:25:02.274984', 'end_time': '2025-09-10T11:25:02.274984', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712'}}
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我应该用哪个工具呢？看看提供的工具，有一个calculator函数，可以处理基本的数学运算，包括加法。所以应该调用这个工具。参数需要表达式字符串，这里就是"2
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph智能体执行完成: c83abcb5-edb0-4064-89c6-a1b06972d20b
2025-09-10 11:25:02.275 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 9320.470ms
2025-09-10 11:26:51.945 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:27:07.040 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 成功认证Java用户: admin
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 初始化LangGraph智能体服务...
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 加载了 3 个工具
2025-09-10 11:27:07.047 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:27:07.047 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph智能体服务初始化完成
2025-09-10 11:27:12.115 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:27:16.001 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:27:16.003 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1', 'type': 'tool_call'}
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:27:16.005689', 'end_time': '2025-09-10T11:27:16.005689', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1'}}
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3，我之前调用了计算器工具，得到的结果是5。现在需要把这个结果用自然语言回复给用户。因为问题很简单，直接告诉用户答案就可以了。不需要额外的解释，保持回答简洁明了。
2025-09-10 11:27:16.007 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:27:16.007 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph智能体执行完成: d645944b-2d13-4f8f-9ff7-b279fd0f5720
2025-09-10 11:27:16.009 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 8976.007ms
2025-09-10 11:30:06.262 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | 成功认证Java用户: admin
2025-09-10 11:30:06.970 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:30:06.973 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 714.962ms
2025-09-10 11:31:07.120 | INFO     | 9745333e2466430180d4e61409ad34cb | 成功认证Java用户: admin
2025-09-10 11:31:07.122 | INFO     | 9745333e2466430180d4e61409ad34cb | 已清空用户 1 的会话 chat_session_1757475006235_x1z2qrujst 历史记录
2025-09-10 11:31:07.122 | INFO     | 9745333e2466430180d4e61409ad34cb | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757475006235_x1z2qrujst | 4.360ms
2025-09-10 11:31:20.586 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | 成功认证Java用户: admin
2025-09-10 11:31:21.121 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:31:21.123 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 537.522ms
2025-09-10 11:31:49.095 | INFO     | 795368a94ca941f09dc54a26871c5f52 | 成功认证Java用户: admin
2025-09-10 11:31:49.491 | INFO     | 795368a94ca941f09dc54a26871c5f52 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:31:49.493 | INFO     | 795368a94ca941f09dc54a26871c5f52 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 399.196ms
2025-09-10 11:32:03.838 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | 成功认证Java用户: admin
2025-09-10 11:32:04.226 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:32:04.227 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 390.748ms
2025-09-10 11:32:36.979 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 成功认证Java用户: admin
2025-09-10 11:32:36.981 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 已清空用户 1 的会话 chat_session_1757475123811_x1gqk61nh0d 历史记录
2025-09-10 11:32:36.981 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757475123811_x1gqk61nh0d | 4.217ms
2025-09-10 11:37:41.840 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:38:21.577 | INFO     | 1eecdce4586d44c0b7788439f2385ebc | 127.0.0.1       | POST     | 401    | /api/iot/v1/chat/chat/stream | 28.946ms
2025-09-10 11:39:03.470 | INFO     | 20cfce1ace9c42878472de3f0d794a71 | 成功认证Java用户: admin
2025-09-10 11:39:03.473 | INFO     | 20cfce1ace9c42878472de3f0d794a71 | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat/stream | 17.221ms
2025-09-10 11:39:14.449 | INFO     | 50610ab0cbb1444ea689d570b2d0f6ce | 成功认证Java用户: admin
2025-09-10 11:39:14.451 | INFO     | 50610ab0cbb1444ea689d570b2d0f6ce | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat/stream | 3.289ms
2025-09-10 11:40:07.410 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | 成功认证Java用户: admin
2025-09-10 11:40:07.849 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:40:07.851 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 443.323ms
2025-09-10 11:41:03.405 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | 成功认证Java用户: admin
2025-09-10 11:41:03.781 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:41:03.783 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.867ms
2025-09-10 11:41:21.404 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | 成功认证Java用户: admin
2025-09-10 11:41:21.799 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:41:21.800 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.753ms
2025-09-10 11:42:42.727 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:43:30.510 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 成功认证Java用户: admin
2025-09-10 11:43:30.514 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 用户 1 发起统一流式聊天
2025-09-10 11:43:30.514 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat/stream | 11.949ms
2025-09-10 11:43:30.515 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 初始化LangGraph智能体服务...
2025-09-10 11:43:30.515 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 加载了 3 个工具
2025-09-10 11:43:30.526 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:43:30.526 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | LangGraph智能体服务初始化完成
2025-09-10 11:43:35.957 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:43:38.948 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-c02bc1bb66f64c3489049ca5814403b8', 'type': 'tool_call'}
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-c02bc1bb66f64c3489049ca5814403b8
2025-09-10 11:43:38.950 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:43:38.950156', 'end_time': '2025-09-10T11:43:38.950156', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-c02bc1bb66f64c3489049ca5814403b8'}}
2025-09-10 11:43:38.951 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:43:38.951 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我之前调用了计算器工具，得到的结果是5。现在需要把结果用中文自然语言回复给用户。应该直接给出答案，不需要多余的解释，因为问题很简单。确认结果正确后，直接说“计
2025-09-10 11:43:38.951 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:43:38.951 | INFO     | c375aa0f6f354b17a3da297a710b9fa7 | LangGraph智能体执行完成: 10d5b7c2-f8cd-4676-81b3-b8458279d1f3
2025-09-10 11:45:07.412 | INFO     | 2a493f5c7d1e46b991a97fd76aaf320b | 成功认证Java用户: admin
2025-09-10 11:45:07.793 | INFO     | 2a493f5c7d1e46b991a97fd76aaf320b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:45:07.795 | INFO     | 2a493f5c7d1e46b991a97fd76aaf320b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 385.155ms
2025-09-10 11:45:31.368 | INFO     | 30705009a836437490977789209a3494 | 成功认证Java用户: admin
2025-09-10 11:45:31.371 | INFO     | 30705009a836437490977789209a3494 | 用户 1 发起统一流式聊天
2025-09-10 11:45:31.371 | INFO     | 30705009a836437490977789209a3494 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.865ms
2025-09-10 11:45:31.372 | INFO     | 30705009a836437490977789209a3494 | 初始化LangGraph智能体服务...
2025-09-10 11:45:31.372 | INFO     | 30705009a836437490977789209a3494 | 加载了 3 个工具
2025-09-10 11:45:31.374 | INFO     | 30705009a836437490977789209a3494 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:45:31.374 | INFO     | 30705009a836437490977789209a3494 | LangGraph智能体服务初始化完成
2025-09-10 11:45:35.449 | INFO     | 30705009a836437490977789209a3494 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:45:41.269 | INFO     | 30705009a836437490977789209a3494 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:45:41.270 | INFO     | 30705009a836437490977789209a3494 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:45:41.270 | INFO     | 30705009a836437490977789209a3494 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:45:41.270 | INFO     | 30705009a836437490977789209a3494 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。因为这是一个基本的加法运算，没有涉及高级函数，所以应该
2025-09-10 11:45:41.270 | INFO     | 30705009a836437490977789209a3494 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:45:41.270 | INFO     | 30705009a836437490977789209a3494 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-c95c5f9e8f864b798c0ad17a447ed254', 'type': 'tool_call'}
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-c95c5f9e8f864b798c0ad17a447ed254
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:45:41.272031', 'end_time': '2025-09-10T11:45:41.272031', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-c95c5f9e8f864b798c0ad17a447ed254'}}
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我应该用哪个工具呢？看提供的工具，有一个calculator函数，可以处理基本的数学运算，包括加法。所以应该调用这个工具。参数需要是一个表达式字符串，所以表达
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:45:41.272 | INFO     | 30705009a836437490977789209a3494 | LangGraph智能体执行完成: d7b0be88-1d83-4650-9b95-1df664b9cb77
2025-09-10 11:46:03.405 | INFO     | 092b87cff5d64272926d2fdcf312dafb | 成功认证Java用户: admin
2025-09-10 11:46:03.803 | INFO     | 092b87cff5d64272926d2fdcf312dafb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:46:03.804 | INFO     | 092b87cff5d64272926d2fdcf312dafb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.513ms
2025-09-10 11:46:24.690 | INFO     | c2d4cb05add44879a4f7deca5bba739e | 成功认证Java用户: admin
2025-09-10 11:46:25.091 | INFO     | c2d4cb05add44879a4f7deca5bba739e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:46:25.092 | INFO     | c2d4cb05add44879a4f7deca5bba739e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 404.241ms
2025-09-10 11:46:36.621 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 成功认证Java用户: admin
2025-09-10 11:46:36.623 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 用户 1 发起统一流式聊天
2025-09-10 11:46:36.624 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 3.622ms
2025-09-10 11:46:36.624 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 初始化LangGraph智能体服务...
2025-09-10 11:46:36.624 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 加载了 3 个工具
2025-09-10 11:46:36.627 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:46:36.627 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | LangGraph智能体服务初始化完成
2025-09-10 11:46:40.629 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:46:45.184 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:46:45.186 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:46:45.186 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 消息 0: 类型=HumanMessage, 内容=你好，请计算3+2
2025-09-10 11:46:45.186 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算3+2。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:46:45.186 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:46:45.186 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '3 + 2'}, 'id': 'chatcmpl-tool-4472380c590f43e888121675a17e8c8c', 'type': 'tool_call'}
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 提取的工具信息: name=calculator, args={'expression': '3 + 2'}, id=chatcmpl-tool-4472380c590f43e888121675a17e8c8c
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '3 + 2'}, 'status': 'success', 'start_time': '2025-09-10T11:46:45.187339', 'end_time': '2025-09-10T11:46:45.187339', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-4472380c590f43e888121675a17e8c8c'}}
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算3加2，我应该用哪个工具呢？看提供的工具，calculator是用来处理基本运算的，而advanced_calculator是高级运算。3+2是简单的加法，所以用ca
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:46:45.187 | INFO     | 17ae9e5ebb864efa83d4f6f0fc3c4611 | LangGraph智能体执行完成: chat_session_1757475984660_p5jhh8gv88h
2025-09-10 11:48:18.898 | INFO     | 1a7680273d584195a70d80033b17b12e | 成功认证Java用户: admin
2025-09-10 11:48:19.293 | INFO     | 1a7680273d584195a70d80033b17b12e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:48:19.294 | INFO     | 1a7680273d584195a70d80033b17b12e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.798ms
2025-09-10 11:48:48.515 | INFO     | e6bdcbd4baa749d2beef885a29c3e3de | 成功认证Java用户: admin
2025-09-10 11:48:48.944 | INFO     | e6bdcbd4baa749d2beef885a29c3e3de | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:48:48.946 | INFO     | e6bdcbd4baa749d2beef885a29c3e3de | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 432.923ms
2025-09-10 11:49:13.191 | INFO     | 107cf81393f346af98a7d00cf5f4e892 | 成功认证Java用户: admin
2025-09-10 11:49:13.678 | INFO     | 107cf81393f346af98a7d00cf5f4e892 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:49:13.680 | INFO     | 107cf81393f346af98a7d00cf5f4e892 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 490.783ms
2025-09-10 11:49:25.034 | INFO     | 13c64f2c258a49639ec4858f3d243f5f | 成功认证Java用户: admin
2025-09-10 11:49:25.481 | INFO     | 13c64f2c258a49639ec4858f3d243f5f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:49:25.482 | INFO     | 13c64f2c258a49639ec4858f3d243f5f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 450.490ms
2025-09-10 11:50:30.026 | INFO     | 74813418894a4e07993dc043e9fb9d21 | 成功认证Java用户: admin
2025-09-10 11:50:30.414 | INFO     | 74813418894a4e07993dc043e9fb9d21 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:50:30.415 | INFO     | 74813418894a4e07993dc043e9fb9d21 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 391.560ms
2025-09-10 11:50:39.612 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 成功认证Java用户: admin
2025-09-10 11:50:39.616 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 用户 1 发起统一流式聊天
2025-09-10 11:50:40.001 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 389.587ms
2025-09-10 11:50:44.006 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:50:44.019 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:50:44.468 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:50:44.469 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | AI服务响应状态码: 200
2025-09-10 11:50:44.469 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 开始处理流式响应数据
2025-09-10 11:50:49.284 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 流式响应结束，原因: stop
2025-09-10 11:50:49.285 | INFO     | 7c25b59efc0e493aa1c2e3cf96196c25 | 流式响应完成，共处理 152 个数据块，总长度: 269
2025-09-10 11:51:06.501 | INFO     | d623011c1a544effbf90e49392179d82 | 成功认证Java用户: admin
2025-09-10 11:51:06.899 | INFO     | d623011c1a544effbf90e49392179d82 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:51:06.901 | INFO     | d623011c1a544effbf90e49392179d82 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.407ms
2025-09-10 11:51:11.914 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 成功认证Java用户: admin
2025-09-10 11:51:11.917 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 用户 1 发起统一流式聊天
2025-09-10 11:51:12.309 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 396.220ms
2025-09-10 11:51:17.667 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:51:17.668 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:51:18.062 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:51:18.062 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | AI服务响应状态码: 200
2025-09-10 11:51:18.062 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 开始处理流式响应数据
2025-09-10 11:51:29.262 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 流式响应结束，原因: stop
2025-09-10 11:51:29.263 | INFO     | 22515795463b4b6f9c10472a1be9ed11 | 流式响应完成，共处理 350 个数据块，总长度: 622
2025-09-10 11:51:40.931 | INFO     | 00fd5196b1554f9eb00d161626dff440 | 成功认证Java用户: admin
2025-09-10 11:51:40.934 | INFO     | 00fd5196b1554f9eb00d161626dff440 | 已清空用户 1 的会话 chat_session_1757476266479_mdq8chf7drs 历史记录
2025-09-10 11:51:40.934 | INFO     | 00fd5196b1554f9eb00d161626dff440 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757476266479_mdq8chf7drs | 3.597ms
2025-09-10 11:51:52.424 | INFO     | 27977011c7884430b298e9c4c7d5342f | 成功认证Java用户: admin
2025-09-10 11:51:52.426 | INFO     | 27977011c7884430b298e9c4c7d5342f | 用户 1 发起统一流式聊天
2025-09-10 11:51:52.427 | INFO     | 27977011c7884430b298e9c4c7d5342f | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.553ms
2025-09-10 11:51:52.428 | INFO     | 27977011c7884430b298e9c4c7d5342f | 初始化LangGraph智能体服务...
2025-09-10 11:51:52.428 | INFO     | 27977011c7884430b298e9c4c7d5342f | 加载了 3 个工具
2025-09-10 11:51:52.431 | INFO     | 27977011c7884430b298e9c4c7d5342f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:51:52.431 | INFO     | 27977011c7884430b298e9c4c7d5342f | LangGraph智能体服务初始化完成
2025-09-10 11:51:56.928 | INFO     | 27977011c7884430b298e9c4c7d5342f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:52:02.579 | INFO     | 27977011c7884430b298e9c4c7d5342f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:52:02.581 | INFO     | 27977011c7884430b298e9c4c7d5342f | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:52:02.581 | INFO     | 27977011c7884430b298e9c4c7d5342f | 消息 0: 类型=HumanMessage, 内容=你好，请计算3+2
2025-09-10 11:52:02.581 | INFO     | 27977011c7884430b298e9c4c7d5342f | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算3+2。这是一个简单的加法运算。我需要先确定应该使用哪个工具。提供的工具有calculator和advanced_calculator。calculator用于基本运
2025-09-10 11:52:02.581 | INFO     | 27977011c7884430b298e9c4c7d5342f | 发现AI消息包含工具调用: 1 个
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '3 + 2'}, 'id': 'chatcmpl-tool-45d5249641d345a6817e1e6c4b8883f3', 'type': 'tool_call'}
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 提取的工具信息: name=calculator, args={'expression': '3 + 2'}, id=chatcmpl-tool-45d5249641d345a6817e1e6c4b8883f3
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '3 + 2'}, 'status': 'success', 'start_time': '2025-09-10T11:52:02.582304', 'end_time': '2025-09-10T11:52:02.582304', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-45d5249641d345a6817e1e6c4b8883f3'}}
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算3加2，我应该用哪个工具呢？看工具列表，有一个calculator函数，专门处理基本的数学运算，支持加减乘除。用户的问题很简单，就是3+2，所以直接调用calcula
2025-09-10 11:52:02.582 | INFO     | 27977011c7884430b298e9c4c7d5342f | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:52:02.583 | INFO     | 27977011c7884430b298e9c4c7d5342f | LangGraph智能体执行完成: chat_session_1757476300937_6c55a1lgd8r
2025-09-10 11:53:55.739 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 成功认证Java用户: admin
2025-09-10 11:53:55.742 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 用户 1 发起统一流式聊天
2025-09-10 11:53:55.743 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 6.725ms
2025-09-10 11:53:55.745 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 初始化LangGraph智能体服务...
2025-09-10 11:53:55.745 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 加载了 3 个工具
2025-09-10 11:53:55.748 | INFO     | aea3f82a50b645919786c1a978f22cc3 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:53:55.749 | INFO     | aea3f82a50b645919786c1a978f22cc3 | LangGraph智能体服务初始化完成
2025-09-10 11:53:59.833 | INFO     | aea3f82a50b645919786c1a978f22cc3 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:54:04.369 | INFO     | aea3f82a50b645919786c1a978f22cc3 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:54:04.372 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:54:04.372 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 消息 0: 类型=HumanMessage, 内容=你好，请计算3+2
2025-09-10 11:54:04.372 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算3+2。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:54:04.373 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:54:04.373 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '3 + 2'}, 'id': 'chatcmpl-tool-39d4cf9a98f84bf18b51459b7b7032a0', 'type': 'tool_call'}
2025-09-10 11:54:04.373 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 提取的工具信息: name=calculator, args={'expression': '3 + 2'}, id=chatcmpl-tool-39d4cf9a98f84bf18b51459b7b7032a0
2025-09-10 11:54:04.374 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '3 + 2'}, 'status': 'success', 'start_time': '2025-09-10T11:54:04.373304', 'end_time': '2025-09-10T11:54:04.373304', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-39d4cf9a98f84bf18b51459b7b7032a0'}}
2025-09-10 11:54:04.374 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:54:04.374 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算3加2，我应该用哪个工具呢？看提供的工具，calculator是用来处理基本运算的，而advanced_calculator是高级运算。这里只是简单的加法，所以应该用
2025-09-10 11:54:04.374 | INFO     | aea3f82a50b645919786c1a978f22cc3 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:54:04.375 | INFO     | aea3f82a50b645919786c1a978f22cc3 | LangGraph智能体执行完成: chat_session_1757476300937_6c55a1lgd8r
2025-09-10 12:46:49.502 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 12:47:15.726 | INFO     | bfc6f4df8e044bbeb25c76394d7ee745 | 成功认证Java用户: admin
2025-09-10 12:47:15.817 | INFO     | bfc6f4df8e044bbeb25c76394d7ee745 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-09-10 12:47:15.819 | INFO     | bfc6f4df8e044bbeb25c76394d7ee745 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/health | 105.171ms
2025-09-10 12:47:17.670 | INFO     | 121238201272410fb6d620c75894ca1c | 成功认证Java用户: admin
2025-09-10 12:47:17.672 | INFO     | 121238201272410fb6d620c75894ca1c | 权限检查通过: user_id=1, permission=knowledge:base:list, path=/api/iot/v1/knowledge-base/list
2025-09-10 12:47:17.690 | INFO     | 121238201272410fb6d620c75894ca1c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 12:47:17.691 | INFO     | 121238201272410fb6d620c75894ca1c | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.380ms
2025-09-10 12:47:19.406 | INFO     | dfda07d361d24c6c8caf4f95bf32c58e | 成功认证Java用户: admin
2025-09-10 12:47:20.093 | INFO     | dfda07d361d24c6c8caf4f95bf32c58e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 12:47:20.096 | INFO     | dfda07d361d24c6c8caf4f95bf32c58e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.332ms
2025-09-10 12:47:33.433 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 成功认证Java用户: admin
2025-09-10 12:47:33.440 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 用户 1 发起统一流式聊天
2025-09-10 12:47:33.442 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 10.903ms
2025-09-10 12:47:33.444 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 初始化LangGraph智能体服务...
2025-09-10 12:47:33.444 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 加载了 3 个工具
2025-09-10 12:47:33.450 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 12:47:33.451 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | LangGraph智能体服务初始化完成
2025-09-10 12:47:39.123 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 12:47:44.478 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 12:47:44.481 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 开始提取工具调用信息，消息数量: 4
2025-09-10 12:47:44.481 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 12:47:44.482 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 12:47:44.482 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 发现AI消息包含工具调用: 1 个
2025-09-10 12:47:44.482 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-570e87eb9f5344d2a2e21ab5820abe6a', 'type': 'tool_call'}
2025-09-10 12:47:44.483 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-570e87eb9f5344d2a2e21ab5820abe6a
2025-09-10 12:47:44.483 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T12:47:44.483711', 'end_time': '2025-09-10T12:47:44.483711', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-570e87eb9f5344d2a2e21ab5820abe6a'}}
2025-09-10 12:47:44.483 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 12:47:44.484 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我之前调用了计算器工具，得到的结果是5。现在需要把这个结果用自然语言回复给用户。首先，我应该直接给出答案，然后可能需要解释一下步骤，确保用户理解。不过这个问题
2025-09-10 12:47:44.484 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 12:47:44.485 | INFO     | f23fb092f9c74dcaa0c7007ce1b77a2e | LangGraph智能体执行完成: chat_session_1757479644138_6tb8nli82t3
2025-09-10 12:47:53.122 | INFO     | 70555a57b37440469ba667add865f835 | 成功认证Java用户: admin
2025-09-10 12:47:53.124 | INFO     | 70555a57b37440469ba667add865f835 | 用户 1 发起统一流式聊天
2025-09-10 12:47:53.125 | INFO     | 70555a57b37440469ba667add865f835 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.161ms
2025-09-10 12:47:53.126 | INFO     | 70555a57b37440469ba667add865f835 | 初始化LangGraph智能体服务...
2025-09-10 12:47:53.126 | INFO     | 70555a57b37440469ba667add865f835 | 加载了 3 个工具
2025-09-10 12:47:53.130 | INFO     | 70555a57b37440469ba667add865f835 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 12:47:53.130 | INFO     | 70555a57b37440469ba667add865f835 | LangGraph智能体服务初始化完成
2025-09-10 12:47:57.195 | INFO     | 70555a57b37440469ba667add865f835 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 12:48:01.174 | INFO     | 70555a57b37440469ba667add865f835 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 12:48:01.177 | INFO     | 70555a57b37440469ba667add865f835 | 开始提取工具调用信息，消息数量: 4
2025-09-10 12:48:01.177 | INFO     | 70555a57b37440469ba667add865f835 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 12:48:01.178 | INFO     | 70555a57b37440469ba667add865f835 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 12:48:01.178 | INFO     | 70555a57b37440469ba667add865f835 | 发现AI消息包含工具调用: 1 个
2025-09-10 12:48:01.178 | INFO     | 70555a57b37440469ba667add865f835 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-03cf8fbe61ac4871a54ab98ce6ad6935', 'type': 'tool_call'}
2025-09-10 12:48:01.179 | INFO     | 70555a57b37440469ba667add865f835 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-03cf8fbe61ac4871a54ab98ce6ad6935
2025-09-10 12:48:01.179 | INFO     | 70555a57b37440469ba667add865f835 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T12:48:01.179078', 'end_time': '2025-09-10T12:48:01.179078', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-03cf8fbe61ac4871a54ab98ce6ad6935'}}
2025-09-10 12:48:01.179 | INFO     | 70555a57b37440469ba667add865f835 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 12:48:01.179 | INFO     | 70555a57b37440469ba667add865f835 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我之前调用了计算器工具，结果得到了5。现在需要把结果以自然的方式告诉他。应该直接给出答案，然后确认是否正确，或者是否需要进一步帮助。用户的问题很简单，可能只需
2025-09-10 12:48:01.179 | INFO     | 70555a57b37440469ba667add865f835 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 12:48:01.180 | INFO     | 70555a57b37440469ba667add865f835 | LangGraph智能体执行完成: chat_session_1757479644138_6tb8nli82t3
2025-09-10 12:52:21.636 | INFO     | e92a8057a03b4b18bfeacce212102582 | 成功认证Java用户: admin
2025-09-10 12:52:22.350 | INFO     | e92a8057a03b4b18bfeacce212102582 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 12:52:22.352 | INFO     | e92a8057a03b4b18bfeacce212102582 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 719.710ms
2025-09-10 12:57:21.633 | INFO     | 00aa68bc787d4e529d98046ae0c18fb6 | 成功认证Java用户: admin
2025-09-10 12:57:22.354 | INFO     | 00aa68bc787d4e529d98046ae0c18fb6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 12:57:22.357 | INFO     | 00aa68bc787d4e529d98046ae0c18fb6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 726.720ms
2025-09-10 13:02:21.640 | INFO     | bc51f70215b24b45a83343a08ce825b3 | 成功认证Java用户: admin
2025-09-10 13:02:22.366 | INFO     | bc51f70215b24b45a83343a08ce825b3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 13:02:22.369 | INFO     | bc51f70215b24b45a83343a08ce825b3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 731.829ms
2025-09-10 13:02:29.791 | INFO     | c9135d140f7943958fa2710b3ae6563a | 成功认证Java用户: admin
2025-09-10 13:02:29.795 | INFO     | c9135d140f7943958fa2710b3ae6563a | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 5.088ms
2025-09-10 13:02:39.782 | INFO     | 9680c99fcbfc410fbcd2c24fe9f7c964 | 成功认证Java用户: admin
2025-09-10 13:02:39.784 | INFO     | 9680c99fcbfc410fbcd2c24fe9f7c964 | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 3.232ms
2025-09-10 13:03:05.682 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 13:03:54.384 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 13:05:43.174 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 成功认证Java用户: admin
2025-09-10 13:05:43.181 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:05:43.181 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 初始化LangGraph智能体服务...
2025-09-10 13:05:43.182 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 加载了 3 个工具
2025-09-10 13:05:43.186 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:05:43.187 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | LangGraph智能体服务初始化完成
2025-09-10 13:05:53.416 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:05:53.427 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 执行知识库查询: health check
2025-09-10 13:05:57.865 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 成功认证Java用户: admin
2025-09-10 13:05:57.869 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:05:57.869 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 初始化LangGraph智能体服务...
2025-09-10 13:05:57.870 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 加载了 3 个工具
2025-09-10 13:05:57.872 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:05:57.872 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | LangGraph智能体服务初始化完成
2025-09-10 13:06:05.798 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:06:05.800 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:06:05.800 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:06:05.800 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入的是“health_check”，我需要先理解这个请求的含义。看起来用户可能想进行某种健康检查，但根据提供的工具列表，我需要确定是否有相关的功能可以调用。

首先，查看可
2025-09-10 13:06:05.800 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 发现AI消息包含工具调用: 1 个
2025-09-10 13:06:05.800 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-6b47cd0461cd40f9b98cd84d30e363f2', 'type': 'tool_call'}
2025-09-10 13:06:05.801 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-6b47cd0461cd40f9b98cd84d30e363f2
2025-09-10 13:06:05.801 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:06:05.801064', 'end_time': '2025-09-10T13:06:05.801064', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-6b47cd0461cd40f9b98cd84d30e363f2'}}
2025-09-10 13:06:05.801 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:06:05.802 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我调用了knowledge_query工具来搜索相关文档，但返回的结果是未找到。现在需要根据这个结果给出回应。

首先，我需要确认用户
2025-09-10 13:06:05.802 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:06:05.802 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | LangGraph智能体执行完成: f35e4608-fdc2-4548-a994-8e2b148d281c
2025-09-10 13:06:05.803 | INFO     | 2248a67f8f694cd5a005a7b3f23d083e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 22642.267ms
2025-09-10 13:06:08.974 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 成功认证Java用户: admin
2025-09-10 13:06:08.976 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:06:08.977 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 初始化LangGraph智能体服务...
2025-09-10 13:06:08.977 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 加载了 3 个工具
2025-09-10 13:06:08.979 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:06:08.979 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | LangGraph智能体服务初始化完成
2025-09-10 13:06:09.011 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:06:09.013 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:06:09.013 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:06:09.013 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这个请求看起来像是要检查系统或服务的健康状态，但用户提供的工具里没有专门的健康检查功能。让我再仔细看看可用的
2025-09-10 13:06:09.014 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:06:09.014 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | LangGraph智能体执行完成: ef0e3125-5aa9-4f78-8d13-6c1fbcc143ed
2025-09-10 13:06:09.015 | INFO     | 80cc2259bd16465fbb91244c91a0a211 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 11151.840ms
2025-09-10 13:06:21.680 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:06:21.681 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:06:21.681 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:06:21.681 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要先理解他们的需求。这个指令看起来像是要求进行健康检查，但具体是指什么类型的健康检查呢？可能是身体健康的建议，或者是系统、设备的健康检
2025-09-10 13:06:21.682 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:06:21.682 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | LangGraph智能体执行完成: 30d75d37-234d-478f-8b9e-6dbf5deeb7c4
2025-09-10 13:06:21.682 | INFO     | d0d65dd637d1489eb71fab3638b7fb14 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 12710.479ms
2025-09-10 13:06:52.624 | INFO     | a03e665f30f9456383959c9a10721111 | 成功认证Java用户: admin
2025-09-10 13:06:52.626 | INFO     | a03e665f30f9456383959c9a10721111 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:06:52.626 | INFO     | a03e665f30f9456383959c9a10721111 | 初始化LangGraph智能体服务...
2025-09-10 13:06:52.626 | INFO     | a03e665f30f9456383959c9a10721111 | 加载了 3 个工具
2025-09-10 13:06:52.629 | INFO     | a03e665f30f9456383959c9a10721111 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:06:52.629 | INFO     | a03e665f30f9456383959c9a10721111 | LangGraph智能体服务初始化完成
2025-09-10 13:07:00.355 | INFO     | a03e665f30f9456383959c9a10721111 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:07:00.357 | INFO     | a03e665f30f9456383959c9a10721111 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:07:00.357 | INFO     | a03e665f30f9456383959c9a10721111 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:07:00.357 | INFO     | a03e665f30f9456383959c9a10721111 | 消息 1: 类型=AIMessage, 内容=<think>
Okay, the user sent "health_check". Let me see what that means. Since it's not a specific qu
2025-09-10 13:07:00.357 | INFO     | a03e665f30f9456383959c9a10721111 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:07:00.358 | INFO     | a03e665f30f9456383959c9a10721111 | LangGraph智能体执行完成: 4435d2bf-a176-49d4-83c4-e22387baa696
2025-09-10 13:07:00.358 | INFO     | a03e665f30f9456383959c9a10721111 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 7735.702ms
2025-09-10 13:07:22.631 | INFO     | 0dbc235111a0494396665184bc0ca30b | 成功认证Java用户: admin
2025-09-10 13:07:22.633 | INFO     | 0dbc235111a0494396665184bc0ca30b | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:07:22.633 | INFO     | 0dbc235111a0494396665184bc0ca30b | 初始化LangGraph智能体服务...
2025-09-10 13:07:22.633 | INFO     | 0dbc235111a0494396665184bc0ca30b | 加载了 3 个工具
2025-09-10 13:07:22.635 | INFO     | 0dbc235111a0494396665184bc0ca30b | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:07:22.635 | INFO     | 0dbc235111a0494396665184bc0ca30b | LangGraph智能体服务初始化完成
2025-09-10 13:07:28.818 | INFO     | 0dbc235111a0494396665184bc0ca30b | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:07:28.820 | INFO     | 0dbc235111a0494396665184bc0ca30b | 执行知识库查询: health check
2025-09-10 13:07:39.805 | INFO     | 0dbc235111a0494396665184bc0ca30b | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:07:39.806 | INFO     | 0dbc235111a0494396665184bc0ca30b | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的请求。我需要先确定他们具体需要什么。首先，检查可用的工具：计算器、高级计算器和知识库查询。健康检查可能涉及计算BMI、体脂率，或者查询
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 发现AI消息包含工具调用: 1 个
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'top_k': 5, 'search_type': 'hybrid'}, 'id': 'chatcmpl-tool-a1d0931e176b47edb59b547f8f5be55f', 'type': 'tool_call'}
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'top_k': 5, 'search_type': 'hybrid'}, id=chatcmpl-tool-a1d0931e176b47edb59b547f8f5be55f
2025-09-10 13:07:39.807 | INFO     | 0dbc235111a0494396665184bc0ca30b | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'top_k': 5, 'search_type': 'hybrid'}, 'status': 'success', 'start_time': '2025-09-10T13:07:39.807820', 'end_time': '2025-09-10T13:07:39.807820', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-a1d0931e176b47edb59b547f8f5be55f'}}
2025-09-10 13:07:39.808 | INFO     | 0dbc235111a0494396665184bc0ca30b | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:07:39.808 | INFO     | 0dbc235111a0494396665184bc0ca30b | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前请求了“health_check”，我尝试用知识库查询工具找相关的健康检查信息，但没找到文档。现在需要处理这个情况。可能需要考虑用户的真实需求是什么。健康检查可能涉及计算
2025-09-10 13:07:39.808 | INFO     | 0dbc235111a0494396665184bc0ca30b | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:07:39.808 | INFO     | 0dbc235111a0494396665184bc0ca30b | LangGraph智能体执行完成: 373b941c-5815-4a18-975f-dd681399cff6
2025-09-10 13:07:39.809 | INFO     | 0dbc235111a0494396665184bc0ca30b | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 17180.120ms
2025-09-10 13:07:52.627 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 成功认证Java用户: admin
2025-09-10 13:07:52.630 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:07:52.631 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 初始化LangGraph智能体服务...
2025-09-10 13:07:52.631 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 加载了 3 个工具
2025-09-10 13:07:52.634 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:07:52.634 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | LangGraph智能体服务初始化完成
2025-09-10 13:08:04.957 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:08:04.959 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 执行知识库查询: health check
2025-09-10 13:08:16.691 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:08:16.693 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:08:16.693 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:08:16.693 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了“health_check”，我需要先理解他们的需求。首先，这个请求看起来像是想要检查某个系统的健康状况，比如应用程序、服务器或者网络服务。不过，查看可用的工具，发现提
2025-09-10 13:08:16.693 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:08:16.694 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-230f0ee3e870431b8057ba83a12c87c3', 'type': 'tool_call'}
2025-09-10 13:08:16.694 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-230f0ee3e870431b8057ba83a12c87c3
2025-09-10 13:08:16.694 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:08:16.694535', 'end_time': '2025-09-10T13:08:16.694535', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-230f0ee3e870431b8057ba83a12c87c3'}}
2025-09-10 13:08:16.695 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:08:16.695 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health check”，我调用了知识库查询工具，但返回的结果是未找到相关文档。现在需要处理这个情况。

首先，我需要确认用户的具体需求。可能他们想了解健康检查
2025-09-10 13:08:16.695 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:08:16.695 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | LangGraph智能体执行完成: fd92f8f9-9398-484e-9ffb-445fabae0d36
2025-09-10 13:08:16.696 | INFO     | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 24070.373ms
2025-09-10 13:08:22.637 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 成功认证Java用户: admin
2025-09-10 13:08:22.639 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:08:22.639 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 初始化LangGraph智能体服务...
2025-09-10 13:08:22.640 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 加载了 3 个工具
2025-09-10 13:08:22.642 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:08:22.642 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | LangGraph智能体服务初始化完成
2025-09-10 13:08:29.878 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:08:29.879 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:08:29.879 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:08:29.880 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们需要什么。首先，这个命令看起来像是一种系统健康检查的请求，可能用于测试工具是否正常工作。接下来，我需要检查可用的工具是否有相
2025-09-10 13:08:29.880 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:08:29.880 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | LangGraph智能体执行完成: f92cfd61-091a-496c-9af4-079a05d94afd
2025-09-10 13:08:29.881 | INFO     | 3ed58e82cec84e97864aaf858e1d0694 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 7245.232ms
2025-09-10 13:08:52.637 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 成功认证Java用户: admin
2025-09-10 13:08:52.639 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:08:52.639 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 初始化LangGraph智能体服务...
2025-09-10 13:08:52.639 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 加载了 3 个工具
2025-09-10 13:08:52.641 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:08:52.642 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | LangGraph智能体服务初始化完成
2025-09-10 13:09:00.508 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:09:00.510 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 执行知识库查询: health check
2025-09-10 13:09:09.132 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:09:09.132 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:09:09.132 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:09:09.134 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要确定他们想要什么。首先，这个命令看起来像是一般的健康检查请求，可能涉及身体状况或系统健康检查。但根据提供的工具，我需要看看是否有相关
2025-09-10 13:09:09.134 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 发现AI消息包含工具调用: 1 个
2025-09-10 13:09:09.134 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-21f03e2726ba4e85ae9b755067976fa3', 'type': 'tool_call'}
2025-09-10 13:09:09.134 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-21f03e2726ba4e85ae9b755067976fa3
2025-09-10 13:09:09.135 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:09:09.135444', 'end_time': '2025-09-10T13:09:09.135444', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-21f03e2726ba4e85ae9b755067976fa3'}}
2025-09-10 13:09:09.135 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:09:09.135 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我调用了知识库查询工具，但返回的结果是未找到相关文档。现在需要处理这个情况，给出合适的回答。

首先，用户可能是在询问健康检查相关的信
2025-09-10 13:09:09.135 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:09:09.135 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | LangGraph智能体执行完成: 0b96e589-7a55-4820-bdb6-09b3ccddae97
2025-09-10 13:09:09.136 | INFO     | 236d01cac2ef4e0cb2133068ce1a647e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 16499.871ms
2025-09-10 13:09:22.633 | INFO     | 20cebc703512403e9e35cc79f7395627 | 成功认证Java用户: admin
2025-09-10 13:09:22.635 | INFO     | 20cebc703512403e9e35cc79f7395627 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:09:22.635 | INFO     | 20cebc703512403e9e35cc79f7395627 | 初始化LangGraph智能体服务...
2025-09-10 13:09:22.636 | INFO     | 20cebc703512403e9e35cc79f7395627 | 加载了 3 个工具
2025-09-10 13:09:22.638 | INFO     | 20cebc703512403e9e35cc79f7395627 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:09:22.638 | INFO     | 20cebc703512403e9e35cc79f7395627 | LangGraph智能体服务初始化完成
2025-09-10 13:09:30.682 | INFO     | 20cebc703512403e9e35cc79f7395627 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:09:30.684 | INFO     | 20cebc703512403e9e35cc79f7395627 | 执行知识库查询: health_check
2025-09-10 13:09:41.219 | INFO     | 20cebc703512403e9e35cc79f7395627 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了一个“health_check”的请求。我需要先理解这个请求的含义。看起来用户可能是在询问健康检查相关的信息，比如如何进行健康检查，或者某个系统的健康状态。但根据提供的
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health_check'}, 'id': 'chatcmpl-tool-304473ae53544e59855b92ba27604fb4', 'type': 'tool_call'}
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 提取的工具信息: name=knowledge_query, args={'query': 'health_check'}, id=chatcmpl-tool-304473ae53544e59855b92ba27604fb4
2025-09-10 13:09:41.221 | INFO     | 20cebc703512403e9e35cc79f7395627 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health_check'}, 'status': 'success', 'start_time': '2025-09-10T13:09:41.221464', 'end_time': '2025-09-10T13:09:41.221464', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-304473ae53544e59855b92ba27604fb4'}}
2025-09-10 13:09:41.222 | INFO     | 20cebc703512403e9e35cc79f7395627 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health_check' 相关的文档
2025-09-10 13:09:41.222 | INFO     | 20cebc703512403e9e35cc79f7395627 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前发送了“health_check”，我尝试用knowledge_query工具查找相关文档，但结果是未找到。现在需要处理这个情况。

首先，用户可能是在询问健康检查相关的
2025-09-10 13:09:41.222 | INFO     | 20cebc703512403e9e35cc79f7395627 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:09:41.222 | INFO     | 20cebc703512403e9e35cc79f7395627 | LangGraph智能体执行完成: fed95736-dc29-4d84-a1dc-6df70c16ce38
2025-09-10 13:09:41.223 | INFO     | 20cebc703512403e9e35cc79f7395627 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 18591.516ms
2025-09-10 13:10:21.634 | INFO     | fc0e042708ef498e94b26defa2452878 | 成功认证Java用户: admin
2025-09-10 13:10:21.636 | INFO     | fc0e042708ef498e94b26defa2452878 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:10:21.636 | INFO     | fc0e042708ef498e94b26defa2452878 | 初始化LangGraph智能体服务...
2025-09-10 13:10:21.636 | INFO     | fc0e042708ef498e94b26defa2452878 | 加载了 3 个工具
2025-09-10 13:10:21.638 | INFO     | fc0e042708ef498e94b26defa2452878 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:10:21.638 | INFO     | fc0e042708ef498e94b26defa2452878 | LangGraph智能体服务初始化完成
2025-09-10 13:10:25.570 | INFO     | fc0e042708ef498e94b26defa2452878 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:10:25.571 | INFO     | fc0e042708ef498e94b26defa2452878 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:10:25.571 | INFO     | fc0e042708ef498e94b26defa2452878 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:10:25.571 | INFO     | fc0e042708ef498e94b26defa2452878 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了一个“health_check”的请求。我需要确定应该调用哪个工具。首先，看看可用的工具：calculator、advanced_calculator和knowledg
2025-09-10 13:10:25.571 | INFO     | fc0e042708ef498e94b26defa2452878 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:10:25.572 | INFO     | fc0e042708ef498e94b26defa2452878 | LangGraph智能体执行完成: 0f173864-2359-498b-a619-43c3a49f6fd8
2025-09-10 13:10:25.572 | INFO     | fc0e042708ef498e94b26defa2452878 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 3941.628ms
2025-09-10 13:11:21.630 | INFO     | 89c7755904804f968bbfb3b43382f98d | 成功认证Java用户: admin
2025-09-10 13:11:21.631 | INFO     | 89c7755904804f968bbfb3b43382f98d | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:11:21.631 | INFO     | 89c7755904804f968bbfb3b43382f98d | 初始化LangGraph智能体服务...
2025-09-10 13:11:21.632 | INFO     | 89c7755904804f968bbfb3b43382f98d | 加载了 3 个工具
2025-09-10 13:11:21.634 | INFO     | 89c7755904804f968bbfb3b43382f98d | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:11:21.634 | INFO     | 89c7755904804f968bbfb3b43382f98d | LangGraph智能体服务初始化完成
2025-09-10 13:11:27.705 | INFO     | 89c7755904804f968bbfb3b43382f98d | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:11:27.707 | INFO     | 89c7755904804f968bbfb3b43382f98d | 执行知识库查询: health check
2025-09-10 13:11:37.856 | INFO     | 89c7755904804f968bbfb3b43382f98d | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:11:37.858 | INFO     | 89c7755904804f968bbfb3b43382f98d | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:11:37.858 | INFO     | 89c7755904804f968bbfb3b43382f98d | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这可能是一个健康检查的请求，但提供的工具里没有直接处理健康检查的函数。现有的工具是计算器、高级计算器和知识库
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 发现AI消息包含工具调用: 1 个
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-52ad1331fb3d467cac064087198bf5ba', 'type': 'tool_call'}
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-52ad1331fb3d467cac064087198bf5ba
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:11:37.859603', 'end_time': '2025-09-10T13:11:37.859603', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-52ad1331fb3d467cac064087198bf5ba'}}
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前请求了“health_check”，我调用了知识库查询功能，但返回的结果是未找到相关文档。现在需要处理这个情况。

首先，用户可能是在询问健康检查相关的信息，比如如何进行
2025-09-10 13:11:37.859 | INFO     | 89c7755904804f968bbfb3b43382f98d | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:11:37.860 | INFO     | 89c7755904804f968bbfb3b43382f98d | LangGraph智能体执行完成: 423f6897-1f0c-44d0-962e-6dfb8aa04d0b
2025-09-10 13:11:37.860 | INFO     | 89c7755904804f968bbfb3b43382f98d | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 16233.137ms
2025-09-10 13:12:21.624 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 成功认证Java用户: admin
2025-09-10 13:12:21.626 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:12:21.626 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 初始化LangGraph智能体服务...
2025-09-10 13:12:21.626 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 加载了 3 个工具
2025-09-10 13:12:21.628 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:12:21.629 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | LangGraph智能体服务初始化完成
2025-09-10 13:12:36.486 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:12:36.488 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 执行知识库查询: health check
2025-09-10 13:12:46.289 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:12:46.291 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:12:46.291 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:12:46.291 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了“health_check”，我需要先理解这个请求的意思。看起来用户可能想进行健康检查，但具体是什么类型的呢？是身体上的健康检查，还是系统、设备之类的？根据提供的工具，
2025-09-10 13:12:46.291 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 发现AI消息包含工具调用: 1 个
2025-09-10 13:12:46.292 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'hybrid'}, 'id': 'chatcmpl-tool-209a3c47892a4d609ebada91b0ad5665', 'type': 'tool_call'}
2025-09-10 13:12:46.292 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'hybrid'}, id=chatcmpl-tool-209a3c47892a4d609ebada91b0ad5665
2025-09-10 13:12:46.292 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'hybrid'}, 'status': 'success', 'start_time': '2025-09-10T13:12:46.292683', 'end_time': '2025-09-10T13:12:46.292683', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-209a3c47892a4d609ebada91b0ad5665'}}
2025-09-10 13:12:46.292 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:12:46.292 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我尝试用知识库查询，但没有找到相关文档。现在需要处理这个情况。首先，用户可能是指医学上的健康检查，但知识库没有相关信息，或者他们可能指
2025-09-10 13:12:46.293 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:12:46.293 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | LangGraph智能体执行完成: 9b410c52-90e6-4e6e-bc7d-3389ce3a2b60
2025-09-10 13:12:46.294 | INFO     | 6bbcbed288f046bfbf4e20137ea5ccee | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 24671.640ms
2025-09-10 13:13:21.630 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 成功认证Java用户: admin
2025-09-10 13:13:21.632 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:13:21.633 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 初始化LangGraph智能体服务...
2025-09-10 13:13:21.633 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 加载了 3 个工具
2025-09-10 13:13:21.635 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:13:21.635 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | LangGraph智能体服务初始化完成
2025-09-10 13:13:28.009 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:13:28.011 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 执行知识库查询: health check
2025-09-10 13:13:37.378 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这个命令可能是指系统健康检查，但用户可能是在询问健康相关的知识，比如如何保持健康或者健康检查的建议。我需要看
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-f3237341dfff4410bbd684853e74388d', 'type': 'tool_call'}
2025-09-10 13:13:37.381 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-f3237341dfff4410bbd684853e74388d
2025-09-10 13:13:37.382 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:13:37.382116', 'end_time': '2025-09-10T13:13:37.382116', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-f3237341dfff4410bbd684853e74388d'}}
2025-09-10 13:13:37.382 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:13:37.382 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前输入了“health_check”，我调用了知识库查询工具，但没有找到相关文档。现在需要处理这个情况。首先，用户可能是在询问健康检查相关的信息，但知识库中没有相关内容，所
2025-09-10 13:13:37.382 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:13:37.382 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | LangGraph智能体执行完成: 35e6dfad-e1f4-4ac4-b99c-96f6c85d858d
2025-09-10 13:13:37.383 | INFO     | ee35a4c84b1641c6aae47a64fb5e0828 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 15754.489ms
2025-09-10 13:14:21.626 | INFO     | 952647a0953146c89165fa8b75b13353 | 成功认证Java用户: admin
2025-09-10 13:14:21.628 | INFO     | 952647a0953146c89165fa8b75b13353 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:14:21.628 | INFO     | 952647a0953146c89165fa8b75b13353 | 初始化LangGraph智能体服务...
2025-09-10 13:14:21.628 | INFO     | 952647a0953146c89165fa8b75b13353 | 加载了 3 个工具
2025-09-10 13:14:21.630 | INFO     | 952647a0953146c89165fa8b75b13353 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:14:21.630 | INFO     | 952647a0953146c89165fa8b75b13353 | LangGraph智能体服务初始化完成
2025-09-10 13:14:30.704 | INFO     | 952647a0953146c89165fa8b75b13353 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:14:30.706 | INFO     | 952647a0953146c89165fa8b75b13353 | 执行知识库查询: health check
2025-09-10 13:14:46.709 | INFO     | 952647a0953146c89165fa8b75b13353 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:14:46.710 | INFO     | 952647a0953146c89165fa8b75b13353 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:14:46.710 | INFO     | 952647a0953146c89165fa8b75b13353 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:14:46.710 | INFO     | 952647a0953146c89165fa8b75b13353 | 消息 1: 类型=AIMessage, 内容=<think>
好的，我现在需要处理用户的问题。用户输入的是“health_check”，看起来是想进行某种健康检查。但根据提供的工具列表，我需要确定是否有对应的函数可以处理这个请求。

首先，查看可
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-e79fe3f422ee41668546724f12942dea', 'type': 'tool_call'}
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-e79fe3f422ee41668546724f12942dea
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:14:46.711847', 'end_time': '2025-09-10T13:14:46.711847', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-e79fe3f422ee41668546724f12942dea'}}
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我尝试用knowledge_query来查找相关文档，但系统返回了“未找到与 'health check' 相关的文档”。这说明当前的知识
2025-09-10 13:14:46.711 | INFO     | 952647a0953146c89165fa8b75b13353 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:14:46.712 | INFO     | 952647a0953146c89165fa8b75b13353 | LangGraph智能体执行完成: 51934f57-2f1b-4de3-b880-1bd674de06db
2025-09-10 13:14:46.712 | INFO     | 952647a0953146c89165fa8b75b13353 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 25088.840ms
2025-09-10 13:15:21.632 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 成功认证Java用户: admin
2025-09-10 13:15:21.635 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:15:21.635 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 初始化LangGraph智能体服务...
2025-09-10 13:15:21.635 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 加载了 3 个工具
2025-09-10 13:15:21.637 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:15:21.637 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | LangGraph智能体服务初始化完成
2025-09-10 13:15:33.354 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:15:33.355 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 执行知识库查询: health check
2025-09-10 13:15:44.605 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:15:44.606 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:15:44.607 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:15:44.607 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们需要什么。首先，检查可用的工具：计算器、高级计算器和知识库查询。健康检查可能涉及计算某种健康指标，比如BMI或者心率，但用户
2025-09-10 13:15:44.607 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:15:44.607 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'hybrid'}, 'id': 'chatcmpl-tool-afbd82385fa54cfd99a8cf6c1d463a5c', 'type': 'tool_call'}
2025-09-10 13:15:44.607 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'hybrid'}, id=chatcmpl-tool-afbd82385fa54cfd99a8cf6c1d463a5c
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'hybrid'}, 'status': 'success', 'start_time': '2025-09-10T13:15:44.608658', 'end_time': '2025-09-10T13:15:44.608658', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-afbd82385fa54cfd99a8cf6c1d463a5c'}}
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前请求了“health_check”，我尝试用知识库查询工具搜索相关文档，但结果是未找到。现在需要处理这个情况。首先，用户可能想了解健康检查的基本信息，比如定义、常见项目或
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | LangGraph智能体执行完成: 0b932581-23ce-439c-80b4-f6f398102970
2025-09-10 13:15:44.608 | INFO     | d65c41d94b9940519f7c17c43eec84c6 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 22979.042ms
2025-09-10 13:16:21.639 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 成功认证Java用户: admin
2025-09-10 13:16:21.643 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:16:21.643 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 初始化LangGraph智能体服务...
2025-09-10 13:16:21.644 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 加载了 3 个工具
2025-09-10 13:16:21.646 | INFO     | 90612e01f77846e893bdfaea25cc8693 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:16:21.646 | INFO     | 90612e01f77846e893bdfaea25cc8693 | LangGraph智能体服务初始化完成
2025-09-10 13:16:26.198 | INFO     | 90612e01f77846e893bdfaea25cc8693 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:16:26.200 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:16:26.201 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:16:26.201 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这个请求看起来像是一种系统健康检查，可能用于测试API是否正常运行。用户可能想确认服务是否可用，或者检查系统
2025-09-10 13:16:26.201 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:16:26.201 | INFO     | 90612e01f77846e893bdfaea25cc8693 | LangGraph智能体执行完成: 27ccd667-160e-491c-aae9-a7e2de317c87
2025-09-10 13:16:26.202 | INFO     | 90612e01f77846e893bdfaea25cc8693 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 4563.886ms
2025-09-10 13:17:21.629 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 成功认证Java用户: admin
2025-09-10 13:17:21.632 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:17:21.632 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 初始化LangGraph智能体服务...
2025-09-10 13:17:21.633 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 加载了 3 个工具
2025-09-10 13:17:21.634 | INFO     | ba72f454974642f28aec7f72683f9eb6 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:17:21.635 | INFO     | ba72f454974642f28aec7f72683f9eb6 | LangGraph智能体服务初始化完成
2025-09-10 13:17:31.796 | INFO     | ba72f454974642f28aec7f72683f9eb6 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:17:31.797 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:17:31.797 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:17:31.797 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要确定他们想要什么。首先，这个指令可能是指健康检查，但要看用户的具体需求。提供的工具里有计算器、高级计算器和知识查询。健康检查可能不属
2025-09-10 13:17:31.797 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:17:31.797 | INFO     | ba72f454974642f28aec7f72683f9eb6 | LangGraph智能体执行完成: 33d77492-0cce-4d0c-9750-91f965abe687
2025-09-10 13:17:31.798 | INFO     | ba72f454974642f28aec7f72683f9eb6 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 10170.857ms
2025-09-10 13:18:21.636 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 成功认证Java用户: admin
2025-09-10 13:18:21.638 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:18:21.638 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 初始化LangGraph智能体服务...
2025-09-10 13:18:21.638 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 加载了 3 个工具
2025-09-10 13:18:21.640 | INFO     | e582aaba8b3944269a9c17f82d08b70e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:18:21.640 | INFO     | e582aaba8b3944269a9c17f82d08b70e | LangGraph智能体服务初始化完成
2025-09-10 13:18:26.809 | INFO     | e582aaba8b3944269a9c17f82d08b70e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:18:26.811 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 执行知识库查询: health_check
2025-09-10 13:18:39.182 | INFO     | e582aaba8b3944269a9c17f82d08b70e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:18:39.184 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:18:39.184 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要确定他们想要什么。首先，看看可用的工具：计算器、高级计算器和知识库查询。健康检查可能不是数学计算，所以可能需要用知识库查询。用户可能
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 发现AI消息包含工具调用: 1 个
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health_check'}, 'id': 'chatcmpl-tool-b501d1236b344b7ab15d00e615903892', 'type': 'tool_call'}
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 提取的工具信息: name=knowledge_query, args={'query': 'health_check'}, id=chatcmpl-tool-b501d1236b344b7ab15d00e615903892
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health_check'}, 'status': 'success', 'start_time': '2025-09-10T13:18:39.185017', 'end_time': '2025-09-10T13:18:39.185017', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-b501d1236b344b7ab15d00e615903892'}}
2025-09-10 13:18:39.185 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 消息 2: 类型=ToolMessage, 内容=未找到与 'health_check' 相关的文档
2025-09-10 13:18:39.186 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前输入了“health_check”，我调用了知识库查询功能，但返回的结果是未找到相关文档。现在需要处理这个情况。

首先，我需要确认用户的需求是什么。可能用户想了解健康检
2025-09-10 13:18:39.186 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:18:39.186 | INFO     | e582aaba8b3944269a9c17f82d08b70e | LangGraph智能体执行完成: ed20191c-4250-4a5a-94f0-322126e4a943
2025-09-10 13:18:39.186 | INFO     | e582aaba8b3944269a9c17f82d08b70e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 17552.484ms
2025-09-10 13:19:21.627 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 成功认证Java用户: admin
2025-09-10 13:19:21.629 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:19:21.630 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 初始化LangGraph智能体服务...
2025-09-10 13:19:21.630 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 加载了 3 个工具
2025-09-10 13:19:21.632 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:19:21.632 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | LangGraph智能体服务初始化完成
2025-09-10 13:19:29.080 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:19:29.082 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:19:29.082 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:19:29.082 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的请求。我需要先确定他们具体需要什么。首先，查看可用的工具：calculator、advanced_calculator和knowled
2025-09-10 13:19:29.082 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:19:29.082 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | LangGraph智能体执行完成: 60a1802a-6daf-41ad-8861-919e2ec65821
2025-09-10 13:19:29.083 | INFO     | 22f6f63b21214e6caa6e5e8dc6268523 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 7458.932ms
2025-09-10 13:20:21.638 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 成功认证Java用户: admin
2025-09-10 13:20:21.640 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:20:21.640 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 初始化LangGraph智能体服务...
2025-09-10 13:20:21.641 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 加载了 3 个工具
2025-09-10 13:20:21.643 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:20:21.643 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | LangGraph智能体服务初始化完成
2025-09-10 13:20:25.631 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:20:25.633 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 执行知识库查询: health_check
2025-09-10 13:20:33.212 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:20:33.214 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:20:33.214 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:20:33.214 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要确定他们想要什么。首先，看看可用的工具：计算器、高级计算器和知识库查询。健康检查可能不是数学计算，所以可能需要用知识库查询。用户可能
2025-09-10 13:20:33.214 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 发现AI消息包含工具调用: 1 个
2025-09-10 13:20:33.215 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health_check'}, 'id': 'chatcmpl-tool-174de7d9a2ae4b0d9e34435e571fd120', 'type': 'tool_call'}
2025-09-10 13:20:33.215 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 提取的工具信息: name=knowledge_query, args={'query': 'health_check'}, id=chatcmpl-tool-174de7d9a2ae4b0d9e34435e571fd120
2025-09-10 13:20:33.216 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health_check'}, 'status': 'success', 'start_time': '2025-09-10T13:20:33.216031', 'end_time': '2025-09-10T13:20:33.216031', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-174de7d9a2ae4b0d9e34435e571fd120'}}
2025-09-10 13:20:33.216 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 消息 2: 类型=ToolMessage, 内容=未找到与 'health_check' 相关的文档
2025-09-10 13:20:33.216 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前输入了“health_check”，我调用了知识库查询，但没有找到相关文档。现在需要处理这个情况。首先，可能需要确认用户的具体需求。健康检查可能指的是医疗健康检查，或者系
2025-09-10 13:20:33.216 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:20:33.216 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | LangGraph智能体执行完成: ede3b3c3-3536-4899-8486-e28b0869a3e3
2025-09-10 13:20:33.217 | INFO     | e45f1ee500074bbfa2828dcc7d37f4ab | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 11580.550ms
2025-09-10 13:21:21.633 | INFO     | 3039a5311049424696b5f04f326653ea | 成功认证Java用户: admin
2025-09-10 13:21:21.635 | INFO     | 3039a5311049424696b5f04f326653ea | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:21:21.635 | INFO     | 3039a5311049424696b5f04f326653ea | 初始化LangGraph智能体服务...
2025-09-10 13:21:21.635 | INFO     | 3039a5311049424696b5f04f326653ea | 加载了 3 个工具
2025-09-10 13:21:21.637 | INFO     | 3039a5311049424696b5f04f326653ea | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:21:21.637 | INFO     | 3039a5311049424696b5f04f326653ea | LangGraph智能体服务初始化完成
2025-09-10 13:21:29.885 | INFO     | 3039a5311049424696b5f04f326653ea | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:21:29.887 | INFO     | 3039a5311049424696b5f04f326653ea | 执行知识库查询: health check
2025-09-10 13:21:40.402 | INFO     | 3039a5311049424696b5f04f326653ea | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:21:40.403 | INFO     | 3039a5311049424696b5f04f326653ea | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的查询。我需要先确定用户的具体需求是什么。首先，这个查询比较简短，可能是指健康检查相关的问题。可能的场景包括：用户想了解如何进行健康检查
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 发现AI消息包含工具调用: 1 个
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, 'id': 'chatcmpl-tool-30882e8ed0b84911a3d0faa45f59b425', 'type': 'tool_call'}
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, id=chatcmpl-tool-30882e8ed0b84911a3d0faa45f59b425
2025-09-10 13:21:40.404 | INFO     | 3039a5311049424696b5f04f326653ea | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, 'status': 'success', 'start_time': '2025-09-10T13:21:40.404684', 'end_time': '2025-09-10T13:21:40.404684', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-30882e8ed0b84911a3d0faa45f59b425'}}
2025-09-10 13:21:40.405 | INFO     | 3039a5311049424696b5f04f326653ea | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:21:40.405 | INFO     | 3039a5311049424696b5f04f326653ea | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我尝试用knowledge_query工具搜索相关文档，但结果是未找到。现在需要处理这个情况。

首先，用户可能期待关于健康检查的信息
2025-09-10 13:21:40.405 | INFO     | 3039a5311049424696b5f04f326653ea | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:21:40.405 | INFO     | 3039a5311049424696b5f04f326653ea | LangGraph智能体执行完成: 5f06ac9f-7340-428b-b45d-6edc8371b997
2025-09-10 13:21:40.406 | INFO     | 3039a5311049424696b5f04f326653ea | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 18775.706ms
2025-09-10 13:22:21.626 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 成功认证Java用户: admin
2025-09-10 13:22:21.629 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:22:21.630 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 初始化LangGraph智能体服务...
2025-09-10 13:22:21.630 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 加载了 3 个工具
2025-09-10 13:22:21.632 | INFO     | 5b17aee7206842d28061ad456feedeb9 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:22:21.632 | INFO     | 5b17aee7206842d28061ad456feedeb9 | LangGraph智能体服务初始化完成
2025-09-10 13:22:30.851 | INFO     | 5b17aee7206842d28061ad456feedeb9 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:22:30.853 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:22:30.853 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:22:30.853 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的请求，我需要先理解他们的需求。首先，这个请求看起来像是一种健康检查，可能是在测试系统是否正常运行，或者是用户想确认他们的查询能否被正确
2025-09-10 13:22:30.854 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:22:30.854 | INFO     | 5b17aee7206842d28061ad456feedeb9 | LangGraph智能体执行完成: c6f6e1ff-56c4-4ffb-baa7-24d9c524c322
2025-09-10 13:22:30.854 | INFO     | 5b17aee7206842d28061ad456feedeb9 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 9229.889ms
2025-09-10 13:23:21.638 | INFO     | 9807cbd387794e5e91c405a638ee222f | 成功认证Java用户: admin
2025-09-10 13:23:21.640 | INFO     | 9807cbd387794e5e91c405a638ee222f | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:23:21.640 | INFO     | 9807cbd387794e5e91c405a638ee222f | 初始化LangGraph智能体服务...
2025-09-10 13:23:21.640 | INFO     | 9807cbd387794e5e91c405a638ee222f | 加载了 3 个工具
2025-09-10 13:23:21.643 | INFO     | 9807cbd387794e5e91c405a638ee222f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:23:21.644 | INFO     | 9807cbd387794e5e91c405a638ee222f | LangGraph智能体服务初始化完成
2025-09-10 13:23:28.232 | INFO     | 9807cbd387794e5e91c405a638ee222f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:23:28.234 | INFO     | 9807cbd387794e5e91c405a638ee222f | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:23:28.234 | INFO     | 9807cbd387794e5e91c405a638ee222f | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:23:28.235 | INFO     | 9807cbd387794e5e91c405a638ee222f | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个"health_check"的请求。我需要先理解这个请求的具体含义。健康检查通常是指系统或服务的状态检查，但用户可能想知道我的运行状态或者是否有任何问题。

首先，
2025-09-10 13:23:28.235 | INFO     | 9807cbd387794e5e91c405a638ee222f | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:23:28.235 | INFO     | 9807cbd387794e5e91c405a638ee222f | LangGraph智能体执行完成: e98450ac-d578-4cd2-ae63-9a839718c792
2025-09-10 13:23:28.236 | INFO     | 9807cbd387794e5e91c405a638ee222f | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 6599.851ms
2025-09-10 13:24:21.638 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 成功认证Java用户: admin
2025-09-10 13:24:21.639 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:24:21.640 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 初始化LangGraph智能体服务...
2025-09-10 13:24:21.640 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 加载了 3 个工具
2025-09-10 13:24:21.642 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:24:21.642 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | LangGraph智能体服务初始化完成
2025-09-10 13:24:33.014 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:24:33.016 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 执行知识库查询: health check
2025-09-10 13:24:47.697 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:24:47.699 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:24:47.699 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:24:47.699 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的请求。我需要先分析这个请求的含义。看起来用户可能想进行某种健康检查，但具体是什么类型的呢？是身体健康的检查建议，还是设备、系统之类的健
2025-09-10 13:24:47.699 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:24:47.699 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'semantic'}, 'id': 'chatcmpl-tool-b66a660e7fe847199c111b2f505c85c7', 'type': 'tool_call'}
2025-09-10 13:24:47.700 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'semantic'}, id=chatcmpl-tool-b66a660e7fe847199c111b2f505c85c7
2025-09-10 13:24:47.700 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'semantic'}, 'status': 'success', 'start_time': '2025-09-10T13:24:47.700509', 'end_time': '2025-09-10T13:24:47.700509', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-b66a660e7fe847199c111b2f505c85c7'}}
2025-09-10 13:24:47.700 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:24:47.700 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前请求了“health_check”，我调用知识库查询工具但没有找到相关文档。现在需要处理这个结果。首先，我需要确认用户的需求到底是什么。可能的情况是用户想了解健康检查的相
2025-09-10 13:24:47.701 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:24:47.701 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | LangGraph智能体执行完成: aac772e5-aabc-4023-b785-2c08bfb5704d
2025-09-10 13:24:47.701 | INFO     | 5b9987cdaaa047ef85e8d5a11a403148 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 26065.896ms
2025-09-10 13:25:21.633 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 成功认证Java用户: admin
2025-09-10 13:25:21.634 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:25:21.634 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 初始化LangGraph智能体服务...
2025-09-10 13:25:21.635 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 加载了 3 个工具
2025-09-10 13:25:21.637 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:25:21.637 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | LangGraph智能体服务初始化完成
2025-09-10 13:25:28.467 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:25:35.420 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:25:35.422 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:25:35.422 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:25:35.422 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了一个“health_check”的请求，我需要确定应该调用哪个工具。首先，查看可用的工具：calculator、advanced_calculator和knowledg
2025-09-10 13:25:35.422 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 2'}, 'id': 'chatcmpl-tool-6263e52fddcf490baa8410da322ccc61', 'type': 'tool_call'}
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 提取的工具信息: name=calculator, args={'expression': '2 + 2'}, id=chatcmpl-tool-6263e52fddcf490baa8410da322ccc61
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 2'}, 'status': 'success', 'start_time': '2025-09-10T13:25:35.423241', 'end_time': '2025-09-10T13:25:35.423241', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-6263e52fddcf490baa8410da322ccc61'}}
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 消息 2: 类型=ToolMessage, 内容=计算结果: 4
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户发来了health_check的请求，我需要处理一下。首先，我应该调用哪个工具呢？看一下可用的工具，calculator、advanced_calculator和knowle
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:25:35.423 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | LangGraph智能体执行完成: 0e6f8581-ec12-49b5-949f-8e5ff0b655e3
2025-09-10 13:25:35.424 | INFO     | 3e06f0cf4e2a4e5b924a9d7c4459f119 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 13793.558ms
2025-09-10 13:26:21.627 | INFO     | 707edddd461a498594558dab886fc27b | 成功认证Java用户: admin
2025-09-10 13:26:21.630 | INFO     | 707edddd461a498594558dab886fc27b | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:26:21.630 | INFO     | 707edddd461a498594558dab886fc27b | 初始化LangGraph智能体服务...
2025-09-10 13:26:21.630 | INFO     | 707edddd461a498594558dab886fc27b | 加载了 3 个工具
2025-09-10 13:26:21.632 | INFO     | 707edddd461a498594558dab886fc27b | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:26:21.632 | INFO     | 707edddd461a498594558dab886fc27b | LangGraph智能体服务初始化完成
2025-09-10 13:26:30.793 | INFO     | 707edddd461a498594558dab886fc27b | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:26:30.795 | INFO     | 707edddd461a498594558dab886fc27b | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:26:30.796 | INFO     | 707edddd461a498594558dab886fc27b | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:26:30.796 | INFO     | 707edddd461a498594558dab886fc27b | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了一个“health_check”的请求。我需要先理解这个请求的含义。通常，健康检查是用来确认系统或服务是否正常运行的。作为智能助手，我本身不需要执行系统级别的健康检查，
2025-09-10 13:26:30.796 | INFO     | 707edddd461a498594558dab886fc27b | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:26:30.796 | INFO     | 707edddd461a498594558dab886fc27b | LangGraph智能体执行完成: 085a80ab-270f-4c2a-8544-06ef03736577
2025-09-10 13:26:30.797 | INFO     | 707edddd461a498594558dab886fc27b | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 9172.027ms
2025-09-10 13:27:21.630 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 成功认证Java用户: admin
2025-09-10 13:27:21.632 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:27:21.632 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 初始化LangGraph智能体服务...
2025-09-10 13:27:21.632 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 加载了 3 个工具
2025-09-10 13:27:21.634 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:27:21.634 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | LangGraph智能体服务初始化完成
2025-09-10 13:27:29.385 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:27:29.386 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:27:29.387 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:27:29.387 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个"health_check"的请求。我需要先确定这个请求的具体含义。首先，这个请求可能是指用户想检查他们的健康状况，或者他们可能是在测试系统是否正常运行。接下来，我
2025-09-10 13:27:29.387 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:27:29.387 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | LangGraph智能体执行完成: 4cbe070f-fe6f-4863-ba60-397cd0b2bce3
2025-09-10 13:27:29.388 | INFO     | 84d57948f3854b8ba423b0e6bbb00c82 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 7760.698ms
2025-09-10 13:28:21.630 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 成功认证Java用户: admin
2025-09-10 13:28:21.632 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:28:21.632 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 初始化LangGraph智能体服务...
2025-09-10 13:28:21.632 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 加载了 3 个工具
2025-09-10 13:28:21.634 | INFO     | ff23655a2d724302bb00892f7bb9c506 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:28:21.634 | INFO     | ff23655a2d724302bb00892f7bb9c506 | LangGraph智能体服务初始化完成
2025-09-10 13:28:29.913 | INFO     | ff23655a2d724302bb00892f7bb9c506 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:28:29.914 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 执行知识库查询: health check
2025-09-10 13:28:39.419 | INFO     | ff23655a2d724302bb00892f7bb9c506 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:28:39.422 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:28:39.422 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:28:39.422 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入了“health_check”，我需要确定他们想要什么。首先，这个词可能指的是健康检查，但要看用户的具体需求。提供的工具里有计算器、高级计算器和知识库查询。如果用户想了解
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'knowledge_base_id': None, 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, 'id': 'chatcmpl-tool-f028592914e549b08b88facdd058764a', 'type': 'tool_call'}
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'knowledge_base_id': None, 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, id=chatcmpl-tool-f028592914e549b08b88facdd058764a
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'knowledge_base_id': None, 'top_k': 5, 'similarity_threshold': 0.7, 'search_type': 'semantic'}, 'status': 'success', 'start_time': '2025-09-10T13:28:39.423636', 'end_time': '2025-09-10T13:28:39.423636', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-f028592914e549b08b88facdd058764a'}}
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前输入了“health_check”，我调用了知识库查询工具，但返回的结果是未找到相关文档。现在需要处理这个情况。

首先，用户可能是在询问健康检查相关的信息，但知识库中没
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:28:39.423 | INFO     | ff23655a2d724302bb00892f7bb9c506 | LangGraph智能体执行完成: 9e7a5c96-c277-465d-a2e0-16daf07302fa
2025-09-10 13:28:39.424 | INFO     | ff23655a2d724302bb00892f7bb9c506 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 17796.773ms
2025-09-10 13:29:21.636 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 成功认证Java用户: admin
2025-09-10 13:29:21.638 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:29:21.638 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 初始化LangGraph智能体服务...
2025-09-10 13:29:21.638 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 加载了 3 个工具
2025-09-10 13:29:21.641 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:29:21.641 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | LangGraph智能体服务初始化完成
2025-09-10 13:29:29.689 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:29:29.691 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 执行知识库查询: health check
2025-09-10 13:29:39.530 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:29:39.531 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:29:39.532 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:29:39.532 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户输入的是“health_check”，我需要先理解他们的需求。首先，这个输入看起来像一个健康检查的请求，但提供的工具中没有直接与健康检查相关的功能。现有的工具分为计算器、高级
2025-09-10 13:29:39.532 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 发现AI消息包含工具调用: 1 个
2025-09-10 13:29:39.532 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'hybrid'}, 'id': 'chatcmpl-tool-3901cb4a86fd4d24adfd4ffc3f2a254a', 'type': 'tool_call'}
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'hybrid'}, id=chatcmpl-tool-3901cb4a86fd4d24adfd4ffc3f2a254a
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'hybrid'}, 'status': 'success', 'start_time': '2025-09-10T13:29:39.533344', 'end_time': '2025-09-10T13:29:39.533344', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-3901cb4a86fd4d24adfd4ffc3f2a254a'}}
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前输入了“health_check”，我尝试用知识库查询工具来找相关信息，但没找到。现在需要处理这个情况。首先，用户可能是在询问健康检查相关的问题，但系统里没有对应的文档。
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:29:39.533 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | LangGraph智能体执行完成: 4e58212f-7437-4c54-af4f-8aa9ce563265
2025-09-10 13:29:39.534 | INFO     | 1096bcf4eada4d6db1e713e3fa7f464d | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 17899.740ms
2025-09-10 13:30:21.630 | INFO     | 95d560c32df74ccda9a148a631bff464 | 成功认证Java用户: admin
2025-09-10 13:30:21.632 | INFO     | 95d560c32df74ccda9a148a631bff464 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:30:21.632 | INFO     | 95d560c32df74ccda9a148a631bff464 | 初始化LangGraph智能体服务...
2025-09-10 13:30:21.633 | INFO     | 95d560c32df74ccda9a148a631bff464 | 加载了 3 个工具
2025-09-10 13:30:21.635 | INFO     | 95d560c32df74ccda9a148a631bff464 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:30:21.635 | INFO     | 95d560c32df74ccda9a148a631bff464 | LangGraph智能体服务初始化完成
2025-09-10 13:30:28.418 | INFO     | 95d560c32df74ccda9a148a631bff464 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:30:28.419 | INFO     | 95d560c32df74ccda9a148a631bff464 | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:30:28.419 | INFO     | 95d560c32df74ccda9a148a631bff464 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:30:28.420 | INFO     | 95d560c32df74ccda9a148a631bff464 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了“health_check”，我需要先理解他们的需求。首先，这个命令看起来像是一种健康检查，可能用于确认系统或服务是否正常运行。接下来，我需要检查可用的工具，看看是否有
2025-09-10 13:30:28.420 | INFO     | 95d560c32df74ccda9a148a631bff464 | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:30:28.420 | INFO     | 95d560c32df74ccda9a148a631bff464 | LangGraph智能体执行完成: fad92f9a-79b3-44d5-88d3-7d2f98d40885
2025-09-10 13:30:28.420 | INFO     | 95d560c32df74ccda9a148a631bff464 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 6792.272ms
2025-09-10 13:30:52.471 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 成功认证Java用户: admin
2025-09-10 13:30:52.473 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:30:52.474 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 初始化LangGraph智能体服务...
2025-09-10 13:30:52.475 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 加载了 3 个工具
2025-09-10 13:30:52.477 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:30:52.478 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | LangGraph智能体服务初始化完成
2025-09-10 13:30:58.190 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:30:58.192 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 开始提取工具调用信息，消息数量: 2
2025-09-10 13:30:58.192 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:30:58.192 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了一个“health_check”的请求。我需要确定应该调用哪个工具来处理这个请求。首先，看看提供的工具：calculator、advanced_calculator和k
2025-09-10 13:30:58.192 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 工具调用信息提取完成，共提取 0 个工具调用
2025-09-10 13:30:58.192 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | LangGraph智能体执行完成: d24057b4-1095-4fdc-a275-5cdbe521c0e0
2025-09-10 13:30:58.193 | INFO     | ef2b062d09c3486390f4a83d64ccf36a | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 5724.197ms
2025-09-10 13:31:22.638 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 成功认证Java用户: admin
2025-09-10 13:31:22.640 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:31:22.640 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 初始化LangGraph智能体服务...
2025-09-10 13:31:22.640 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 加载了 3 个工具
2025-09-10 13:31:22.642 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:31:22.642 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | LangGraph智能体服务初始化完成
2025-09-10 13:31:30.358 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:31:30.360 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 执行知识库查询: health check
2025-09-10 13:31:40.625 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:31:40.626 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:31:40.626 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这个词通常指健康检查，可能与身体状况或系统健康状态有关。但查看可用的工具，有计算器、高级计算器和知识查询。

2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'hybrid'}, 'id': 'chatcmpl-tool-85d3964a3966422ebc7aaa6d67add5d5', 'type': 'tool_call'}
2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'hybrid'}, id=chatcmpl-tool-85d3964a3966422ebc7aaa6d67add5d5
2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'hybrid'}, 'status': 'success', 'start_time': '2025-09-10T13:31:40.627398', 'end_time': '2025-09-10T13:31:40.627398', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-85d3964a3966422ebc7aaa6d67add5d5'}}
2025-09-10 13:31:40.627 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:31:40.628 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我尝试用知识查询工具搜索，但没有找到相关文档。现在需要处理这个情况。

首先，用户可能是指健康检查，但知识库里没有相关信息。可能需要考
2025-09-10 13:31:40.628 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:31:40.628 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | LangGraph智能体执行完成: 0c7adc73-8928-49d4-9bc7-960787a577f4
2025-09-10 13:31:40.628 | INFO     | 612bf675d7234af98b42765d6f2d19f0 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 17992.111ms
2025-09-10 13:31:52.639 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 成功认证Java用户: admin
2025-09-10 13:31:52.641 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:31:52.641 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 初始化LangGraph智能体服务...
2025-09-10 13:31:52.641 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 加载了 3 个工具
2025-09-10 13:31:52.643 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:31:52.643 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | LangGraph智能体服务初始化完成
2025-09-10 13:32:01.716 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:32:01.718 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 执行知识库查询: health check
2025-09-10 13:32:13.838 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:32:13.839 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:32:13.839 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:32:13.839 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发送了“health_check”，我需要确定他们想要什么。首先，这个词通常指健康检查，可能与身体状况或系统健康状态有关。但根据提供的工具，我需要看看是否有相关的功能可以调用
2025-09-10 13:32:13.839 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 发现AI消息包含工具调用: 1 个
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check', 'search_type': 'hybrid', 'top_k': 5}, 'id': 'chatcmpl-tool-1a085c8e5c3f4f8fbbb3914e3955282d', 'type': 'tool_call'}
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 提取的工具信息: name=knowledge_query, args={'query': 'health check', 'search_type': 'hybrid', 'top_k': 5}, id=chatcmpl-tool-1a085c8e5c3f4f8fbbb3914e3955282d
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check', 'search_type': 'hybrid', 'top_k': 5}, 'status': 'success', 'start_time': '2025-09-10T13:32:13.840811', 'end_time': '2025-09-10T13:32:13.840811', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-1a085c8e5c3f4f8fbbb3914e3955282d'}}
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我尝试用knowledge_query工具搜索，但没找到相关文档。现在需要处理这个结果。首先，用户可能是在询问健康检查的一般信息，或者
2025-09-10 13:32:13.840 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:32:13.841 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | LangGraph智能体执行完成: 03bd571b-1178-48e2-bad5-d5e00f932145
2025-09-10 13:32:13.841 | INFO     | 7957f4856e9b440a9871afcd2a641b6b | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 21204.196ms
2025-09-10 13:32:45.120 | INFO     | 84b34ab481094b2abaf8bf2ed0dbd9d0 | 成功认证Java用户: admin
2025-09-10 13:32:45.121 | INFO     | 84b34ab481094b2abaf8bf2ed0dbd9d0 | 192.168.66.13   | GET      | 404    | /api/iot/v1/chat/health | 4.000ms
2025-09-10 13:33:16.311 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 13:33:17.686 | INFO     | 4b2ea7d3a09b4759955c9b2ab546cbd1 | 成功认证Java用户: admin
2025-09-10 13:33:17.688 | INFO     | 4b2ea7d3a09b4759955c9b2ab546cbd1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 8.966ms
2025-09-10 13:33:21.634 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 成功认证Java用户: admin
2025-09-10 13:33:21.637 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 用户 1 发起统一聊天: health_check... (模式: agent)
2025-09-10 13:33:21.637 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 初始化LangGraph智能体服务...
2025-09-10 13:33:21.637 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 加载了 3 个工具
2025-09-10 13:33:21.640 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:33:21.640 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | LangGraph智能体服务初始化完成
2025-09-10 13:33:32.731 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:33:32.740 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 执行知识库查询: health check
2025-09-10 13:33:36.499 | INFO     | 9105d25880bb49a2b2ba2c42ecf9f6fc | 成功认证Java用户: admin
2025-09-10 13:33:36.501 | INFO     | 9105d25880bb49a2b2ba2c42ecf9f6fc | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.353ms
2025-09-10 13:33:43.408 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:33:43.411 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:33:43.411 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 消息 0: 类型=HumanMessage, 内容=health_check
2025-09-10 13:33:43.411 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户发来了一个“health_check”的请求。我需要先理解这个请求的具体含义。首先，这可能是一个拼写错误，用户可能想询问健康检查相关的信息，或者是指某个系统的健康状况检查。但
2025-09-10 13:33:43.412 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 发现AI消息包含工具调用: 1 个
2025-09-10 13:33:43.412 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'knowledge_query', 'args': {'query': 'health check'}, 'id': 'chatcmpl-tool-3b81a85f12de416da0bd55db13610d0f', 'type': 'tool_call'}
2025-09-10 13:33:43.413 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 提取的工具信息: name=knowledge_query, args={'query': 'health check'}, id=chatcmpl-tool-3b81a85f12de416da0bd55db13610d0f
2025-09-10 13:33:43.413 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 成功添加工具调用信息: {'tool_name': 'knowledge_query', 'tool_display_name': 'knowledge_query', 'inputs': {'query': 'health check'}, 'status': 'success', 'start_time': '2025-09-10T13:33:43.413332', 'end_time': '2025-09-10T13:33:43.413332', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-3b81a85f12de416da0bd55db13610d0f'}}
2025-09-10 13:33:43.413 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 消息 2: 类型=ToolMessage, 内容=未找到与 'health check' 相关的文档
2025-09-10 13:33:43.413 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户之前询问了“health_check”，我调用了知识查询工具，但返回的结果是未找到相关文档。现在需要处理这个情况。

首先，我需要确认用户的需求是什么。用户可能是在询问健康检
2025-09-10 13:33:43.414 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:33:43.414 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | LangGraph智能体执行完成: e62a360c-e5f3-4646-967c-fc8aff396525
2025-09-10 13:33:43.416 | INFO     | 49b6b472c53a4b2fa6fd04f4803e9d4f | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat | 21782.963ms
2025-09-10 13:34:00.154 | INFO     | c724357e3cf6441d80689b063ab7924e | 成功认证Java用户: admin
2025-09-10 13:34:00.156 | INFO     | c724357e3cf6441d80689b063ab7924e | 127.0.0.1       | GET      | 200    | /api/iot/v1/chat/health | 3.381ms
2025-09-10 13:34:06.624 | INFO     | e61b7b5b03c442179762ac16b395e946 | 成功认证Java用户: admin
2025-09-10 13:34:06.626 | INFO     | e61b7b5b03c442179762ac16b395e946 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.052ms
2025-09-10 13:34:23.482 | INFO     | e5e7d6161a88471f8fca392680f3b80e | 成功认证Java用户: admin
2025-09-10 13:34:23.485 | INFO     | e5e7d6161a88471f8fca392680f3b80e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.326ms
2025-09-10 13:34:48.436 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 成功认证Java用户: admin
2025-09-10 13:34:48.439 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 用户 1 发起统一流式聊天
2025-09-10 13:34:48.441 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 7.071ms
2025-09-10 13:34:48.442 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 初始化LangGraph智能体服务...
2025-09-10 13:34:48.443 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 加载了 3 个工具
2025-09-10 13:34:48.446 | INFO     | 47655022d9e9464db031c99b7c6ce985 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:34:48.447 | INFO     | 47655022d9e9464db031c99b7c6ce985 | LangGraph智能体服务初始化完成
2025-09-10 13:34:53.242 | INFO     | 47655022d9e9464db031c99b7c6ce985 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:34:53.496 | INFO     | 3d088c5395ac4858a3d17e12be3537ad | 成功认证Java用户: admin
2025-09-10 13:34:53.499 | INFO     | 3d088c5395ac4858a3d17e12be3537ad | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.536ms
2025-09-10 13:34:58.463 | INFO     | 47655022d9e9464db031c99b7c6ce985 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:34:58.466 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 开始提取工具调用信息，消息数量: 4
2025-09-10 13:34:58.466 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 消息 0: 类型=HumanMessage, 内容=你好，请计算3+2
2025-09-10 13:34:58.466 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算3+2。这看起来是一个简单的加法运算。我需要确定应该使用哪个工具。提供的工具有calculator和advanced_calculator。calculator处理基
2025-09-10 13:34:58.466 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 发现AI消息包含工具调用: 1 个
2025-09-10 13:34:58.467 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '3 + 2'}, 'id': 'chatcmpl-tool-9565424a2c064f02bb7a77b3cbfa8059', 'type': 'tool_call'}
2025-09-10 13:34:58.467 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 提取的工具信息: name=calculator, args={'expression': '3 + 2'}, id=chatcmpl-tool-9565424a2c064f02bb7a77b3cbfa8059
2025-09-10 13:34:58.467 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '3 + 2'}, 'status': 'success', 'start_time': '2025-09-10T13:34:58.467377', 'end_time': '2025-09-10T13:34:58.467377', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-9565424a2c064f02bb7a77b3cbfa8059'}}
2025-09-10 13:34:58.467 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 13:34:58.468 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算3加2，我之前调用了计算器工具，得到了结果5。现在需要确认这个结果是否正确，并且回答用户。首先，检查计算器的运算是否正确，3加2确实是5，没问题。然后，用户可能需要一
2025-09-10 13:34:58.468 | INFO     | 47655022d9e9464db031c99b7c6ce985 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 13:34:58.468 | INFO     | 47655022d9e9464db031c99b7c6ce985 | LangGraph智能体执行完成: chat_session_1757482463468_mlczl9amuhj
2025-09-10 13:38:22.096 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 13:38:23.641 | INFO     | 64e37a71063d4bba8a4585b7ec4befc2 | 成功认证Java用户: admin
2025-09-10 13:38:23.644 | INFO     | 64e37a71063d4bba8a4585b7ec4befc2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 9.659ms
2025-09-10 13:39:20.849 | INFO     | 1083df6f80b64b53a486d0c9d78853c3 | 成功认证Java用户: admin
2025-09-10 13:39:20.851 | INFO     | 1083df6f80b64b53a486d0c9d78853c3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.358ms
2025-09-10 13:42:24.077 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 13:43:25.872 | INFO     | 3e36731d881e443aa0e7434a5b359f07 | 成功认证Java用户: admin
2025-09-10 13:43:25.875 | INFO     | 3e36731d881e443aa0e7434a5b359f07 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 11.339ms
2025-09-10 13:43:39.865 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | 成功认证Java用户: admin
2025-09-10 13:43:39.868 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | 用户 1 发起统一流式聊天
2025-09-10 13:43:39.870 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 6.054ms
2025-09-10 13:43:39.870 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | 初始化LangGraph智能体服务...
2025-09-10 13:43:39.870 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | 加载了 3 个工具
2025-09-10 13:43:39.874 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:43:39.874 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | LangGraph智能体服务初始化完成
2025-09-10 13:43:45.935 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:43:51.795 | INFO     | 51ac5c4f3ba0480ab22579c881a5c059 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:43:55.885 | INFO     | 27c80281024a4d03ae2cbfec75af16dd | 成功认证Java用户: admin
2025-09-10 13:43:55.887 | INFO     | 27c80281024a4d03ae2cbfec75af16dd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.773ms
2025-09-10 13:44:02.172 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | 成功认证Java用户: admin
2025-09-10 13:44:02.174 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | 用户 1 发起统一流式聊天
2025-09-10 13:44:02.176 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.971ms
2025-09-10 13:44:02.176 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | 初始化LangGraph智能体服务...
2025-09-10 13:44:02.176 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | 加载了 3 个工具
2025-09-10 13:44:02.178 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:44:02.179 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | LangGraph智能体服务初始化完成
2025-09-10 13:44:05.997 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:44:10.802 | INFO     | 5c817e4b79c84289aa712dcb199aafb5 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:44:26.628 | INFO     | c7bb8d770c2e4733a005bd90e3f302c1 | 成功认证Java用户: admin
2025-09-10 13:44:26.630 | INFO     | c7bb8d770c2e4733a005bd90e3f302c1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.042ms
2025-09-10 13:44:56.630 | INFO     | 493445452f1440ae9e8dd54dcbd465ea | 成功认证Java用户: admin
2025-09-10 13:44:56.632 | INFO     | 493445452f1440ae9e8dd54dcbd465ea | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.477ms
2025-09-10 13:45:26.633 | INFO     | 3c11e7efd1a349ad956b344a5961d36a | 成功认证Java用户: admin
2025-09-10 13:45:26.635 | INFO     | 3c11e7efd1a349ad956b344a5961d36a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.519ms
2025-09-10 13:45:56.633 | INFO     | 544a41215b054379b15076080120e1dd | 成功认证Java用户: admin
2025-09-10 13:45:56.634 | INFO     | 544a41215b054379b15076080120e1dd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.914ms
2025-09-10 13:46:26.632 | INFO     | a6b8c572847c42bcb25aaf911a015446 | 成功认证Java用户: admin
2025-09-10 13:46:26.634 | INFO     | a6b8c572847c42bcb25aaf911a015446 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.953ms
2025-09-10 13:47:21.638 | INFO     | b52e9fb7ced04f139e9183359a1f4fb0 | 成功认证Java用户: admin
2025-09-10 13:47:21.640 | INFO     | b52e9fb7ced04f139e9183359a1f4fb0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.613ms
2025-09-10 13:47:41.665 | INFO     | 4e2e650190694568a22c410e40a35fdb | 成功认证Java用户: admin
2025-09-10 13:47:41.667 | INFO     | 4e2e650190694568a22c410e40a35fdb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.368ms
2025-09-10 13:47:51.853 | INFO     | 21a9b90d24364efaad14bad7a64d2f1f | 成功认证Java用户: admin
2025-09-10 13:47:51.854 | INFO     | 21a9b90d24364efaad14bad7a64d2f1f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.486ms
2025-09-10 13:47:58.083 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | 成功认证Java用户: admin
2025-09-10 13:47:58.085 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | 用户 1 发起统一流式聊天
2025-09-10 13:47:58.085 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.024ms
2025-09-10 13:47:58.086 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | 初始化LangGraph智能体服务...
2025-09-10 13:47:58.086 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | 加载了 3 个工具
2025-09-10 13:47:58.089 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 13:47:58.089 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | LangGraph智能体服务初始化完成
2025-09-10 13:48:01.993 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:48:04.303 | INFO     | 773ab2aa84f547488183bbcdc3cd6d0e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 13:48:22.630 | INFO     | d497025f5d064f30a88b202f83a18cdf | 成功认证Java用户: admin
2025-09-10 13:48:22.632 | INFO     | d497025f5d064f30a88b202f83a18cdf | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.080ms
2025-09-10 13:48:52.629 | INFO     | aa008a05d9e64c7ca8b3d374e1016bfb | 成功认证Java用户: admin
2025-09-10 13:48:52.632 | INFO     | aa008a05d9e64c7ca8b3d374e1016bfb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.842ms
2025-09-10 13:49:22.625 | INFO     | 00b1a373b193485fbce232231b0d6573 | 成功认证Java用户: admin
2025-09-10 13:49:22.627 | INFO     | 00b1a373b193485fbce232231b0d6573 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.349ms
2025-09-10 13:49:52.631 | INFO     | e01095a3166647dbaffe16b628f30ea0 | 成功认证Java用户: admin
2025-09-10 13:49:52.633 | INFO     | e01095a3166647dbaffe16b628f30ea0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.108ms
2025-09-10 13:50:22.632 | INFO     | 5a1a680f438049a8bb3cde8c7a3afe3f | 成功认证Java用户: admin
2025-09-10 13:50:22.634 | INFO     | 5a1a680f438049a8bb3cde8c7a3afe3f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.424ms
2025-09-10 13:50:52.628 | INFO     | 4f57e8a5187b4d3982961de84b9dbda8 | 成功认证Java用户: admin
2025-09-10 13:50:52.631 | INFO     | 4f57e8a5187b4d3982961de84b9dbda8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.418ms
2025-09-10 13:52:21.633 | INFO     | 416ed93074e24ebfa96471eb818c24c5 | 成功认证Java用户: admin
2025-09-10 13:52:21.635 | INFO     | 416ed93074e24ebfa96471eb818c24c5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.535ms
2025-09-10 13:53:21.637 | INFO     | 4c33be6de6024000b665dbe5f023cc83 | 成功认证Java用户: admin
2025-09-10 13:53:21.639 | INFO     | 4c33be6de6024000b665dbe5f023cc83 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.217ms
2025-09-10 13:54:07.392 | INFO     | cbbec9f2f75349ca83f2c94407880425 | 成功认证Java用户: admin
2025-09-10 13:54:07.394 | INFO     | cbbec9f2f75349ca83f2c94407880425 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.508ms
2025-09-10 13:54:22.634 | INFO     | 44834a00677b4671ae933bf203c04aa6 | 成功认证Java用户: admin
2025-09-10 13:54:22.635 | INFO     | 44834a00677b4671ae933bf203c04aa6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.261ms
2025-09-10 13:54:52.636 | INFO     | acd70964def24ee8ac2ea97ccd41d04d | 成功认证Java用户: admin
2025-09-10 13:54:52.639 | INFO     | acd70964def24ee8ac2ea97ccd41d04d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.468ms
2025-09-10 13:56:21.630 | INFO     | 99e5d4981a624297af1179f1fe0105fa | 成功认证Java用户: admin
2025-09-10 13:56:21.632 | INFO     | 99e5d4981a624297af1179f1fe0105fa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.002ms
2025-09-10 13:57:21.634 | INFO     | 4ffbf7dc7cac409695cc114b9bda88d6 | 成功认证Java用户: admin
2025-09-10 13:57:21.637 | INFO     | 4ffbf7dc7cac409695cc114b9bda88d6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.880ms
2025-09-10 13:58:21.642 | INFO     | 6fd0d7a10c9c428ab90ddb14945951f9 | 成功认证Java用户: admin
2025-09-10 13:58:21.644 | INFO     | 6fd0d7a10c9c428ab90ddb14945951f9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.479ms
2025-09-10 13:59:21.629 | INFO     | e9436618498541049ad4a27c326d15c0 | 成功认证Java用户: admin
2025-09-10 13:59:21.632 | INFO     | e9436618498541049ad4a27c326d15c0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.384ms
2025-09-10 14:00:21.644 | INFO     | afa5ba6730a9420f94dffd8ce1f76d7e | 成功认证Java用户: admin
2025-09-10 14:00:21.646 | INFO     | afa5ba6730a9420f94dffd8ce1f76d7e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.364ms
2025-09-10 14:01:21.641 | INFO     | 20ed1d2fba2546e1b03257eb4d4a58a1 | 成功认证Java用户: admin
2025-09-10 14:01:21.644 | INFO     | 20ed1d2fba2546e1b03257eb4d4a58a1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.254ms
2025-09-10 14:02:21.632 | INFO     | a16b6f1552ef4b18a446e14dc5bb997b | 成功认证Java用户: admin
2025-09-10 14:02:21.634 | INFO     | a16b6f1552ef4b18a446e14dc5bb997b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.321ms
2025-09-10 14:03:21.641 | INFO     | 947342eb0bab4c34963aed5e7ab8d348 | 成功认证Java用户: admin
2025-09-10 14:03:21.643 | INFO     | 947342eb0bab4c34963aed5e7ab8d348 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.711ms
2025-09-10 14:04:21.635 | INFO     | 1c706351538f4028adb365182cf1ede6 | 成功认证Java用户: admin
2025-09-10 14:04:21.637 | INFO     | 1c706351538f4028adb365182cf1ede6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.128ms
2025-09-10 14:05:21.630 | INFO     | e386207a4143401b9ffb67cce8988414 | 成功认证Java用户: admin
2025-09-10 14:05:21.632 | INFO     | e386207a4143401b9ffb67cce8988414 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.124ms
2025-09-10 14:05:49.793 | INFO     | e9f21857719c4b76b31d75492548ddeb | 成功认证Java用户: admin
2025-09-10 14:05:49.796 | INFO     | e9f21857719c4b76b31d75492548ddeb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.582ms
2025-09-10 14:06:20.636 | INFO     | 4544ba2da75e4cefa38ee56bee787867 | 成功认证Java用户: admin
2025-09-10 14:06:20.638 | INFO     | 4544ba2da75e4cefa38ee56bee787867 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.787ms
2025-09-10 14:06:21.633 | INFO     | a1433cba2b104f61bf1d142bdcf7b020 | 成功认证Java用户: admin
2025-09-10 14:06:21.636 | INFO     | a1433cba2b104f61bf1d142bdcf7b020 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.359ms
2025-09-10 14:06:50.628 | INFO     | 4274a9b2c5464d62b99af9e80b06b880 | 成功认证Java用户: admin
2025-09-10 14:06:50.631 | INFO     | 4274a9b2c5464d62b99af9e80b06b880 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.542ms
2025-09-10 14:07:20.632 | INFO     | 211d71577dd841a690713abc9af47a6a | 成功认证Java用户: admin
2025-09-10 14:07:20.634 | INFO     | 211d71577dd841a690713abc9af47a6a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.964ms
2025-09-10 14:07:21.629 | INFO     | a48ac4bf9baf4100bf7a59709b2e5180 | 成功认证Java用户: admin
2025-09-10 14:07:21.631 | INFO     | a48ac4bf9baf4100bf7a59709b2e5180 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.398ms
2025-09-10 14:07:50.625 | INFO     | f849c5f40da04f328e3d8f1b374becd2 | 成功认证Java用户: admin
2025-09-10 14:07:50.626 | INFO     | f849c5f40da04f328e3d8f1b374becd2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.785ms
2025-09-10 14:08:08.150 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:08:20.640 | INFO     | 7fde3d01682b4c1fb02ac1a93d7dade3 | 成功认证Java用户: admin
2025-09-10 14:08:20.642 | INFO     | 7fde3d01682b4c1fb02ac1a93d7dade3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 9.324ms
2025-09-10 14:08:21.627 | INFO     | 8ba8d87696a74cc5b489445f25d02003 | 成功认证Java用户: admin
2025-09-10 14:08:21.629 | INFO     | 8ba8d87696a74cc5b489445f25d02003 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.292ms
2025-09-10 14:08:44.210 | INFO     | da910e8ed7d5420395e23bad58c5ac61 | 成功认证Java用户: admin
2025-09-10 14:08:44.211 | INFO     | da910e8ed7d5420395e23bad58c5ac61 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.681ms
2025-09-10 14:08:51.442 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | 成功认证Java用户: admin
2025-09-10 14:08:51.446 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | 用户 1 发起统一流式聊天
2025-09-10 14:08:51.447 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.589ms
2025-09-10 14:08:52.449 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | 初始化LangGraph智能体服务...
2025-09-10 14:08:52.450 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | 加载了 3 个工具
2025-09-10 14:08:52.463 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:08:52.463 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | LangGraph智能体服务初始化完成
2025-09-10 14:09:14.219 | INFO     | a436aee3bb7742238e3c8295011c35df | 成功认证Java用户: admin
2025-09-10 14:09:14.221 | INFO     | a436aee3bb7742238e3c8295011c35df | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.087ms
2025-09-10 14:09:24.800 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:09:24.800 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | Retrying request to /chat/completions in 0.382217 seconds
2025-09-10 14:09:44.634 | INFO     | 1d846ebd24874399973a4d0aa88b5e42 | 成功认证Java用户: admin
2025-09-10 14:09:44.636 | INFO     | 1d846ebd24874399973a4d0aa88b5e42 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.492ms
2025-09-10 14:09:55.730 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:09:55.730 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | Retrying request to /chat/completions in 0.799309 seconds
2025-09-10 14:10:04.849 | INFO     | 2cb205a23e4a40399bf5c07e0c161595 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:10:14.632 | INFO     | 318e3c584b92470d8132679eb904037d | 成功认证Java用户: admin
2025-09-10 14:10:14.634 | INFO     | 318e3c584b92470d8132679eb904037d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.560ms
2025-09-10 14:10:20.695 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:10:28.899 | INFO     | 0e4f531ccbde46269fb16ea15c834223 | 成功认证Java用户: admin
2025-09-10 14:10:28.902 | INFO     | 0e4f531ccbde46269fb16ea15c834223 | 已清空用户 1 的会话 chat_session_1757484524196_19bt00smcoz 历史记录
2025-09-10 14:10:28.902 | INFO     | 0e4f531ccbde46269fb16ea15c834223 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757484524196_19bt00smcoz | 14.095ms
2025-09-10 14:10:35.730 | INFO     | d8ef449d20a342d781c6f08a54d84411 | 成功认证Java用户: admin
2025-09-10 14:10:35.734 | INFO     | d8ef449d20a342d781c6f08a54d84411 | 用户 1 发起统一流式聊天
2025-09-10 14:10:35.735 | INFO     | d8ef449d20a342d781c6f08a54d84411 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.702ms
2025-09-10 14:10:36.744 | INFO     | d8ef449d20a342d781c6f08a54d84411 | 初始化LangGraph智能体服务...
2025-09-10 14:10:36.745 | INFO     | d8ef449d20a342d781c6f08a54d84411 | 加载了 3 个工具
2025-09-10 14:10:36.749 | INFO     | d8ef449d20a342d781c6f08a54d84411 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:10:36.749 | INFO     | d8ef449d20a342d781c6f08a54d84411 | LangGraph智能体服务初始化完成
2025-09-10 14:10:43.519 | INFO     | d8ef449d20a342d781c6f08a54d84411 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:10:44.221 | INFO     | 8b696d2be70b4b82a7dddeda157d9f2e | 成功认证Java用户: admin
2025-09-10 14:10:44.224 | INFO     | 8b696d2be70b4b82a7dddeda157d9f2e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.607ms
2025-09-10 14:10:46.909 | INFO     | d8ef449d20a342d781c6f08a54d84411 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:11:14.218 | INFO     | 99e8429d23884cb694c5287259770e43 | 成功认证Java用户: admin
2025-09-10 14:11:14.220 | INFO     | 99e8429d23884cb694c5287259770e43 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.384ms
2025-09-10 14:11:44.628 | INFO     | 8b3e8522fb4c461ea1116002f75d44a5 | 成功认证Java用户: admin
2025-09-10 14:11:44.630 | INFO     | 8b3e8522fb4c461ea1116002f75d44a5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.068ms
2025-09-10 14:12:14.647 | INFO     | 93da65ecc71e4bc4908fb539e34550cf | 成功认证Java用户: admin
2025-09-10 14:12:14.650 | INFO     | 93da65ecc71e4bc4908fb539e34550cf | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.838ms
2025-09-10 14:12:32.493 | INFO     | a2f3cd13e3c540089de71c30a7692c25 | 成功认证Java用户: admin
2025-09-10 14:12:32.496 | INFO     | a2f3cd13e3c540089de71c30a7692c25 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.537ms
2025-09-10 14:12:44.218 | INFO     | 6e10a1b137a0421ea48a7857ab63ba9e | 成功认证Java用户: admin
2025-09-10 14:12:44.220 | INFO     | 6e10a1b137a0421ea48a7857ab63ba9e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.065ms
2025-09-10 14:13:02.640 | INFO     | 67a3601368dd4d7da0b860c7d1065dd2 | 成功认证Java用户: admin
2025-09-10 14:13:02.644 | INFO     | 67a3601368dd4d7da0b860c7d1065dd2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.245ms
2025-09-10 14:13:14.633 | INFO     | 4e86f25a125c4878ab689ee736ba1a5a | 成功认证Java用户: admin
2025-09-10 14:13:14.636 | INFO     | 4e86f25a125c4878ab689ee736ba1a5a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.583ms
2025-09-10 14:13:32.626 | INFO     | c67d752fadfb44dcaca7ae9fe9ca5178 | 成功认证Java用户: admin
2025-09-10 14:13:32.629 | INFO     | c67d752fadfb44dcaca7ae9fe9ca5178 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.306ms
2025-09-10 14:14:02.634 | INFO     | a1805f60f3ea41988e3d186b6f3b48b3 | 成功认证Java用户: admin
2025-09-10 14:14:02.637 | INFO     | a1805f60f3ea41988e3d186b6f3b48b3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.617ms
2025-09-10 14:14:21.630 | INFO     | 045a4c78556242b48f4ef767255291ac | 成功认证Java用户: admin
2025-09-10 14:14:21.633 | INFO     | 045a4c78556242b48f4ef767255291ac | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.980ms
2025-09-10 14:14:32.634 | INFO     | 5beee104a7a441f7b30cffaa7e260177 | 成功认证Java用户: admin
2025-09-10 14:14:32.636 | INFO     | 5beee104a7a441f7b30cffaa7e260177 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.659ms
2025-09-10 14:15:02.638 | INFO     | f240857f1b784532b5323a020646a5f8 | 成功认证Java用户: admin
2025-09-10 14:15:02.640 | INFO     | f240857f1b784532b5323a020646a5f8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.941ms
2025-09-10 14:15:21.633 | INFO     | f7dccd229ed640a7bfb1406a566fe370 | 成功认证Java用户: admin
2025-09-10 14:15:21.637 | INFO     | f7dccd229ed640a7bfb1406a566fe370 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.872ms
2025-09-10 14:15:32.628 | INFO     | 62ef0409eb2b4c6db3a06faa51b2e7f8 | 成功认证Java用户: admin
2025-09-10 14:15:32.630 | INFO     | 62ef0409eb2b4c6db3a06faa51b2e7f8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.768ms
2025-09-10 14:16:21.634 | INFO     | 17c740f8014546a19ff70cb2f6eab579 | 成功认证Java用户: admin
2025-09-10 14:16:21.637 | INFO     | 17c740f8014546a19ff70cb2f6eab579 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.797ms
2025-09-10 14:16:21.642 | INFO     | fdae3ba862954ac08b0d564d1a99b446 | 成功认证Java用户: admin
2025-09-10 14:16:21.644 | INFO     | fdae3ba862954ac08b0d564d1a99b446 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.736ms
2025-09-10 14:17:21.642 | INFO     | dcc489b2654f4d95ab9cea2e71bf9398 | 成功认证Java用户: admin
2025-09-10 14:17:21.644 | INFO     | dcc489b2654f4d95ab9cea2e71bf9398 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.592ms
2025-09-10 14:17:21.650 | INFO     | 32b42fa3027b4eaf9ca071eaaa1acb1e | 成功认证Java用户: admin
2025-09-10 14:17:21.652 | INFO     | 32b42fa3027b4eaf9ca071eaaa1acb1e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.620ms
2025-09-10 14:17:41.734 | INFO     | 8b97e3fd8f1f4884a29c6a82bcbbd797 | 成功认证Java用户: admin
2025-09-10 14:17:41.738 | INFO     | 8b97e3fd8f1f4884a29c6a82bcbbd797 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.188ms
2025-09-10 14:17:44.631 | INFO     | 2103b6c9f03a4a46994e3a080e85931f | 成功认证Java用户: admin
2025-09-10 14:17:44.633 | INFO     | 2103b6c9f03a4a46994e3a080e85931f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.337ms
2025-09-10 14:18:02.640 | INFO     | be7446480eac4d648e1e89a5c4a7b54d | 成功认证Java用户: admin
2025-09-10 14:18:02.643 | INFO     | be7446480eac4d648e1e89a5c4a7b54d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.092ms
2025-09-10 14:18:14.626 | INFO     | 2427e4bdbfa14c6db69ac1aef91cfa2b | 成功认证Java用户: admin
2025-09-10 14:18:14.627 | INFO     | 2427e4bdbfa14c6db69ac1aef91cfa2b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.671ms
2025-09-10 14:18:32.626 | INFO     | 74d6ffaad0424aedad581b2b769eef8d | 成功认证Java用户: admin
2025-09-10 14:18:32.628 | INFO     | 74d6ffaad0424aedad581b2b769eef8d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.082ms
2025-09-10 14:18:40.013 | INFO     | 5d686204b2354ee0858fbd2ac8de0e36 | 成功认证Java用户: admin
2025-09-10 14:18:40.017 | INFO     | 5d686204b2354ee0858fbd2ac8de0e36 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.647ms
2025-09-10 14:18:58.852 | INFO     | 3eaffb55e20b418ea1190dcec92adc3a | 成功认证Java用户: admin
2025-09-10 14:18:58.854 | INFO     | 3eaffb55e20b418ea1190dcec92adc3a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.133ms
2025-09-10 14:19:10.638 | INFO     | 2c22cc1684a246ff88825b976cd09706 | 成功认证Java用户: admin
2025-09-10 14:19:10.639 | INFO     | 2c22cc1684a246ff88825b976cd09706 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.078ms
2025-09-10 14:19:21.179 | INFO     | e49a7a25c2cf4acba756f10a12c6c927 | 成功认证Java用户: admin
2025-09-10 14:19:21.182 | INFO     | e49a7a25c2cf4acba756f10a12c6c927 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.202ms
2025-09-10 14:19:21.637 | INFO     | c8c71d4355d9466ebae7dceb311ffffa | 成功认证Java用户: admin
2025-09-10 14:19:21.639 | INFO     | c8c71d4355d9466ebae7dceb311ffffa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.238ms
2025-09-10 14:19:21.643 | INFO     | 8e575dd692ed477385e4a3c2fbf81758 | 成功认证Java用户: admin
2025-09-10 14:19:21.645 | INFO     | 8e575dd692ed477385e4a3c2fbf81758 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.503ms
2025-09-10 14:19:29.632 | INFO     | d2ea67b7668b4e2ea8c1db9473a15606 | 成功认证Java用户: admin
2025-09-10 14:19:29.634 | INFO     | d2ea67b7668b4e2ea8c1db9473a15606 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.818ms
2025-09-10 14:19:40.630 | INFO     | a0f08363561c4db09d9e19aead5ff54d | 成功认证Java用户: admin
2025-09-10 14:19:40.633 | INFO     | a0f08363561c4db09d9e19aead5ff54d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.424ms
2025-09-10 14:19:55.725 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:19:58.381 | INFO     | 593ec8f56b8e4857b632925b49fa5f41 | 成功认证Java用户: admin
2025-09-10 14:19:58.384 | INFO     | 593ec8f56b8e4857b632925b49fa5f41 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 17.149ms
2025-09-10 14:19:58.390 | INFO     | be8a02a461574a8f8c616314a8ac9e82 | 成功认证Java用户: admin
2025-09-10 14:19:58.394 | INFO     | be8a02a461574a8f8c616314a8ac9e82 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.123ms
2025-09-10 14:20:06.058 | INFO     | 82df2eed938f470ba6c9d1d4c47757c8 | 成功认证Java用户: admin
2025-09-10 14:20:06.060 | INFO     | 82df2eed938f470ba6c9d1d4c47757c8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.868ms
2025-09-10 14:20:14.285 | INFO     | e33d25e5b58c4dc086d4f41814773104 | 成功认证Java用户: admin
2025-09-10 14:20:14.289 | INFO     | e33d25e5b58c4dc086d4f41814773104 | 用户 1 发起统一流式聊天
2025-09-10 14:20:14.290 | INFO     | e33d25e5b58c4dc086d4f41814773104 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 7.114ms
2025-09-10 14:20:15.301 | INFO     | e33d25e5b58c4dc086d4f41814773104 | 初始化LangGraph智能体服务...
2025-09-10 14:20:15.301 | INFO     | e33d25e5b58c4dc086d4f41814773104 | 加载了 3 个工具
2025-09-10 14:20:15.307 | INFO     | e33d25e5b58c4dc086d4f41814773104 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:20:15.307 | INFO     | e33d25e5b58c4dc086d4f41814773104 | LangGraph智能体服务初始化完成
2025-09-10 14:20:36.072 | INFO     | f86e4642051740dc8c216f422ab194bb | 成功认证Java用户: admin
2025-09-10 14:20:36.073 | INFO     | f86e4642051740dc8c216f422ab194bb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.253ms
2025-09-10 14:20:47.684 | INFO     | e33d25e5b58c4dc086d4f41814773104 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:20:47.684 | INFO     | e33d25e5b58c4dc086d4f41814773104 | Retrying request to /chat/completions in 0.449693 seconds
2025-09-10 14:20:59.175 | INFO     | e33d25e5b58c4dc086d4f41814773104 | Retrying request to /chat/completions in 0.910444 seconds
2025-09-10 14:21:06.069 | INFO     | 60471ac2c1b94d508e2bfc8f28a62cee | 成功认证Java用户: admin
2025-09-10 14:21:06.072 | INFO     | 60471ac2c1b94d508e2bfc8f28a62cee | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.045ms
2025-09-10 14:21:30.667 | INFO     | e33d25e5b58c4dc086d4f41814773104 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:21:38.732 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:21:45.370 | INFO     | 090d8b40501f430e9caa949fefdf6d8d | 成功认证Java用户: admin
2025-09-10 14:21:45.373 | INFO     | 090d8b40501f430e9caa949fefdf6d8d | 已清空用户 1 的会话 chat_session_1757485206045_hnbze40v8vh 历史记录
2025-09-10 14:21:45.374 | INFO     | 090d8b40501f430e9caa949fefdf6d8d | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485206045_hnbze40v8vh | 11.535ms
2025-09-10 14:21:50.887 | INFO     | 59c615bb252d49689309c1713f1f1e3f | 成功认证Java用户: admin
2025-09-10 14:21:50.891 | INFO     | 59c615bb252d49689309c1713f1f1e3f | 用户 1 发起统一流式聊天
2025-09-10 14:21:50.891 | INFO     | 59c615bb252d49689309c1713f1f1e3f | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.763ms
2025-09-10 14:21:51.919 | INFO     | 59c615bb252d49689309c1713f1f1e3f | 初始化LangGraph智能体服务...
2025-09-10 14:21:51.919 | INFO     | 59c615bb252d49689309c1713f1f1e3f | 加载了 3 个工具
2025-09-10 14:21:51.924 | INFO     | 59c615bb252d49689309c1713f1f1e3f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:21:51.924 | INFO     | 59c615bb252d49689309c1713f1f1e3f | LangGraph智能体服务初始化完成
2025-09-10 14:22:06.549 | INFO     | 718e8ea778774facbd052a9b1a2b1632 | 成功认证Java用户: admin
2025-09-10 14:22:06.552 | INFO     | 718e8ea778774facbd052a9b1a2b1632 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.916ms
2025-09-10 14:22:24.168 | INFO     | 59c615bb252d49689309c1713f1f1e3f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:22:24.168 | INFO     | 59c615bb252d49689309c1713f1f1e3f | Retrying request to /chat/completions in 0.452390 seconds
2025-09-10 14:22:34.456 | INFO     | 59c615bb252d49689309c1713f1f1e3f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:22:34.457 | INFO     | 59c615bb252d49689309c1713f1f1e3f | Retrying request to /chat/completions in 0.984255 seconds
2025-09-10 14:22:35.493 | INFO     | 59c615bb252d49689309c1713f1f1e3f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:22:36.638 | INFO     | 2773e286be31473fa25c4885399f830d | 成功认证Java用户: admin
2025-09-10 14:22:36.640 | INFO     | 2773e286be31473fa25c4885399f830d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.681ms
2025-09-10 14:22:43.605 | INFO     | 9453e85c1b5b4bba96706e86dcee7aa1 | 成功认证Java用户: admin
2025-09-10 14:22:43.607 | INFO     | 9453e85c1b5b4bba96706e86dcee7aa1 | 已清空用户 1 的会话 chat_session_1757485305385_a9b44ibjqk 历史记录
2025-09-10 14:22:43.607 | INFO     | 9453e85c1b5b4bba96706e86dcee7aa1 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485305385_a9b44ibjqk | 3.487ms
2025-09-10 14:22:51.041 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | 成功认证Java用户: admin
2025-09-10 14:22:51.043 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | 用户 1 发起统一流式聊天
2025-09-10 14:22:51.044 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.276ms
2025-09-10 14:22:52.070 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | 初始化LangGraph智能体服务...
2025-09-10 14:22:52.071 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | 加载了 3 个工具
2025-09-10 14:22:52.074 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:22:52.074 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | LangGraph智能体服务初始化完成
2025-09-10 14:22:52.101 | INFO     | ad7b11ff942746998b38c4f5ba9fd7a4 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:23:06.073 | INFO     | e75246d7df5f4aa6b10aa929b2f9632b | 成功认证Java用户: admin
2025-09-10 14:23:06.076 | INFO     | e75246d7df5f4aa6b10aa929b2f9632b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.712ms
2025-09-10 14:23:09.771 | INFO     | 9e1437611f2047469bc3312851af7ea6 | 成功认证Java用户: admin
2025-09-10 14:23:09.773 | INFO     | 9e1437611f2047469bc3312851af7ea6 | 已清空用户 1 的会话 chat_session_1757485363610_36pbjb5icgc 历史记录
2025-09-10 14:23:09.773 | INFO     | 9e1437611f2047469bc3312851af7ea6 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485363610_36pbjb5icgc | 4.084ms
2025-09-10 14:23:13.724 | INFO     | 78583cf26842461082fdfbf48699a4cc | 成功认证Java用户: admin
2025-09-10 14:23:13.727 | INFO     | 78583cf26842461082fdfbf48699a4cc | 用户 1 发起统一流式聊天
2025-09-10 14:23:13.728 | INFO     | 78583cf26842461082fdfbf48699a4cc | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.926ms
2025-09-10 14:23:14.742 | INFO     | 78583cf26842461082fdfbf48699a4cc | 初始化LangGraph智能体服务...
2025-09-10 14:23:14.743 | INFO     | 78583cf26842461082fdfbf48699a4cc | 加载了 3 个工具
2025-09-10 14:23:14.746 | INFO     | 78583cf26842461082fdfbf48699a4cc | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:23:14.746 | INFO     | 78583cf26842461082fdfbf48699a4cc | LangGraph智能体服务初始化完成
2025-09-10 14:23:14.769 | INFO     | 78583cf26842461082fdfbf48699a4cc | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:23:36.073 | INFO     | a40980b4e64f419a9761733113997e84 | 成功认证Java用户: admin
2025-09-10 14:23:36.075 | INFO     | a40980b4e64f419a9761733113997e84 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.451ms
2025-09-10 14:24:06.636 | INFO     | ba614f2b3ed44edd82fdeae3ed8fcf38 | 成功认证Java用户: admin
2025-09-10 14:24:06.637 | INFO     | ba614f2b3ed44edd82fdeae3ed8fcf38 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.575ms
2025-09-10 14:24:36.636 | INFO     | 7fa7e464d40b42f0b74ad2b688e1b8bd | 成功认证Java用户: admin
2025-09-10 14:24:36.638 | INFO     | 7fa7e464d40b42f0b74ad2b688e1b8bd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.785ms
2025-09-10 14:25:06.635 | INFO     | 2cd12c4b4d66472397d9c2b411afb160 | 成功认证Java用户: admin
2025-09-10 14:25:06.637 | INFO     | 2cd12c4b4d66472397d9c2b411afb160 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.295ms
2025-09-10 14:25:42.148 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:25:44.029 | INFO     | c88a97aead7349b0bc6e9d3a2f2482b6 | 成功认证Java用户: admin
2025-09-10 14:25:44.032 | INFO     | c88a97aead7349b0bc6e9d3a2f2482b6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 9.198ms
2025-09-10 14:25:47.472 | INFO     | c8f81adeacab476da3ddb0533f4a5b29 | 成功认证Java用户: admin
2025-09-10 14:25:47.475 | INFO     | c8f81adeacab476da3ddb0533f4a5b29 | 已清空用户 1 的会话 chat_session_1757485389776_syokty24q5a 历史记录
2025-09-10 14:25:47.476 | INFO     | c8f81adeacab476da3ddb0533f4a5b29 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485389776_syokty24q5a | 5.831ms
2025-09-10 14:25:53.607 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | 成功认证Java用户: admin
2025-09-10 14:25:53.612 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | 用户 1 发起统一流式聊天
2025-09-10 14:25:53.613 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 7.457ms
2025-09-10 14:25:54.608 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | 初始化LangGraph智能体服务...
2025-09-10 14:25:54.608 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | 加载了 3 个工具
2025-09-10 14:25:54.614 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:25:54.614 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | LangGraph智能体服务初始化完成
2025-09-10 14:26:06.071 | INFO     | cab718c13bd14d87a6f18ed9bebfcb06 | 成功认证Java用户: admin
2025-09-10 14:26:06.074 | INFO     | cab718c13bd14d87a6f18ed9bebfcb06 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.407ms
2025-09-10 14:26:26.959 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:26:26.960 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | Retrying request to /chat/completions in 0.409268 seconds
2025-09-10 14:26:36.627 | INFO     | 09f7f5c49914440e943e452e95230da4 | 成功认证Java用户: admin
2025-09-10 14:26:36.629 | INFO     | 09f7f5c49914440e943e452e95230da4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.634ms
2025-09-10 14:26:46.836 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:26:46.837 | INFO     | 8d752e0050de4cbd8a776c13f0227377 | Retrying request to /chat/completions in 0.925055 seconds
2025-09-10 14:26:55.730 | INFO     | 8d2aa445a3ac437381b7c2dc75d350fc | 成功认证Java用户: admin
2025-09-10 14:26:55.731 | INFO     | 8d2aa445a3ac437381b7c2dc75d350fc | 已清空用户 1 的会话 chat_session_1757485547479_b4qct9i6n26 历史记录
2025-09-10 14:26:55.732 | INFO     | 8d2aa445a3ac437381b7c2dc75d350fc | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485547479_b4qct9i6n26 | 4.107ms
2025-09-10 14:26:59.744 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | 成功认证Java用户: admin
2025-09-10 14:26:59.747 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | 用户 1 发起统一流式聊天
2025-09-10 14:26:59.749 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.223ms
2025-09-10 14:27:00.763 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | 初始化LangGraph智能体服务...
2025-09-10 14:27:00.764 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | 加载了 3 个工具
2025-09-10 14:27:00.766 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:27:00.767 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | LangGraph智能体服务初始化完成
2025-09-10 14:27:00.791 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:27:06.069 | INFO     | 73277f21a7aa479ba999b6230b82626c | 成功认证Java用户: admin
2025-09-10 14:27:06.072 | INFO     | 73277f21a7aa479ba999b6230b82626c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.546ms
2025-09-10 14:27:07.435 | INFO     | 8c9543e23c0640e0a178f338a5e40594 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:27:29.672 | INFO     | f3c1fbbce6fe40749ff5f54dfc7e20c9 | 成功认证Java用户: admin
2025-09-10 14:27:29.674 | INFO     | f3c1fbbce6fe40749ff5f54dfc7e20c9 | 已清空用户 1 的会话 chat_session_1757485615734_fy52fthjw24 历史记录
2025-09-10 14:27:29.675 | INFO     | f3c1fbbce6fe40749ff5f54dfc7e20c9 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757485615734_fy52fthjw24 | 3.977ms
2025-09-10 14:27:34.329 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | 成功认证Java用户: admin
2025-09-10 14:27:34.330 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | 用户 1 发起统一流式聊天
2025-09-10 14:27:34.332 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.573ms
2025-09-10 14:27:35.367 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | 初始化LangGraph智能体服务...
2025-09-10 14:27:35.367 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | 加载了 3 个工具
2025-09-10 14:27:35.370 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:27:35.371 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | LangGraph智能体服务初始化完成
2025-09-10 14:27:35.395 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:27:36.069 | INFO     | f71c4210507d4e34a1302bc7c5db16af | 成功认证Java用户: admin
2025-09-10 14:27:36.071 | INFO     | f71c4210507d4e34a1302bc7c5db16af | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.506ms
2025-09-10 14:27:39.941 | INFO     | 4d34193d1d224a5495f1c6e6ff8f1468 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:28:06.069 | INFO     | a5c3e6b8fec64d49b0091e519d3aecf4 | 成功认证Java用户: admin
2025-09-10 14:28:06.071 | INFO     | a5c3e6b8fec64d49b0091e519d3aecf4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.555ms
2025-09-10 14:28:36.632 | INFO     | 5c38af4d31dd404496800d2e5d1cc3ac | 成功认证Java用户: admin
2025-09-10 14:28:36.633 | INFO     | 5c38af4d31dd404496800d2e5d1cc3ac | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.470ms
2025-09-10 14:29:06.634 | INFO     | eb41384b82f54748951292bd62cec6be | 成功认证Java用户: admin
2025-09-10 14:29:06.636 | INFO     | eb41384b82f54748951292bd62cec6be | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.102ms
2025-09-10 14:30:21.636 | INFO     | ef35abc645b8454dbbcb959f87001081 | 成功认证Java用户: admin
2025-09-10 14:30:21.639 | INFO     | ef35abc645b8454dbbcb959f87001081 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.861ms
2025-09-10 14:30:43.630 | INFO     | 40ec36cba7d246bdbe90e3670e288409 | 成功认证Java用户: admin
2025-09-10 14:30:43.635 | INFO     | 40ec36cba7d246bdbe90e3670e288409 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.441ms
2025-09-10 14:30:53.808 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:31:06.655 | INFO     | bb0fd12960614c7eab93976199b36443 | 成功认证Java用户: admin
2025-09-10 14:31:06.661 | INFO     | bb0fd12960614c7eab93976199b36443 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 22.878ms
2025-09-10 14:31:36.630 | INFO     | 1a2f5c65af824dadb605eab6bffb3b6d | 成功认证Java用户: admin
2025-09-10 14:31:36.631 | INFO     | 1a2f5c65af824dadb605eab6bffb3b6d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.048ms
2025-09-10 14:32:21.631 | INFO     | 59e3592187694d2196ff5c8904ac3d84 | 成功认证Java用户: admin
2025-09-10 14:32:21.633 | INFO     | 59e3592187694d2196ff5c8904ac3d84 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.679ms
2025-09-10 14:33:21.632 | INFO     | 54425d2eaa274055a56beea7e1cadbc8 | 成功认证Java用户: admin
2025-09-10 14:33:21.635 | INFO     | 54425d2eaa274055a56beea7e1cadbc8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.463ms
2025-09-10 14:34:21.629 | INFO     | df5689653fb84d658437a3cc4d6fb72f | 成功认证Java用户: admin
2025-09-10 14:34:21.631 | INFO     | df5689653fb84d658437a3cc4d6fb72f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.453ms
2025-09-10 14:35:13.225 | INFO     | 75740ded93ac4d9ab279da4e9eac7518 | 成功认证Java用户: admin
2025-09-10 14:35:13.228 | INFO     | 75740ded93ac4d9ab279da4e9eac7518 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.244ms
2025-09-10 14:45:18.697 | INFO     | eb068b2a21d24b12aa77bd35b7a3f456 | 成功认证Java用户: admin
2025-09-10 14:45:18.701 | INFO     | eb068b2a21d24b12aa77bd35b7a3f456 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.969ms
2025-09-10 14:46:24.113 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:46:34.291 | INFO     | 6fd9ff4ed97641fabb277acdaaf246cb | 成功认证Java用户: admin
2025-09-10 14:46:34.295 | INFO     | 6fd9ff4ed97641fabb277acdaaf246cb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 14.223ms
2025-09-10 14:46:43.269 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | 成功认证Java用户: admin
2025-09-10 14:46:43.272 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | 用户 1 发起统一流式聊天
2025-09-10 14:46:43.273 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.739ms
2025-09-10 14:46:44.304 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | 初始化LangGraph智能体服务...
2025-09-10 14:46:44.304 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | 加载了 3 个工具
2025-09-10 14:46:44.309 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:46:44.310 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | LangGraph智能体服务初始化完成
2025-09-10 14:46:59.100 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 14:46:59.101 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | Retrying request to /chat/completions in 0.479978 seconds
2025-09-10 14:46:59.607 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:47:04.302 | INFO     | d15069406c9345b4a30cea711223dbb8 | 成功认证Java用户: admin
2025-09-10 14:47:04.305 | INFO     | d15069406c9345b4a30cea711223dbb8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.584ms
2025-09-10 14:47:04.560 | INFO     | 2a54423c1c814b5c8f1de7a2549819db | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:47:34.319 | INFO     | 19683efd1d0a42efadf5d3a1029f8e73 | 成功认证Java用户: admin
2025-09-10 14:47:34.322 | INFO     | 19683efd1d0a42efadf5d3a1029f8e73 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.228ms
2025-09-10 14:48:04.627 | INFO     | d0822d82d6b6430cae46cdc0c8c9a86d | 成功认证Java用户: admin
2025-09-10 14:48:04.629 | INFO     | d0822d82d6b6430cae46cdc0c8c9a86d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.168ms
2025-09-10 14:48:34.635 | INFO     | e4c57d5a4d4a469da0c880c765e8d2db | 成功认证Java用户: admin
2025-09-10 14:48:34.636 | INFO     | e4c57d5a4d4a469da0c880c765e8d2db | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.072ms
2025-09-10 14:49:04.627 | INFO     | 902de1cdbd3948908b471403e2c56141 | 成功认证Java用户: admin
2025-09-10 14:49:04.629 | INFO     | 902de1cdbd3948908b471403e2c56141 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.201ms
2025-09-10 14:49:34.299 | INFO     | 1e58b2d862864118b91a0b09b3f3d88a | 成功认证Java用户: admin
2025-09-10 14:49:34.301 | INFO     | 1e58b2d862864118b91a0b09b3f3d88a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.835ms
2025-09-10 14:49:34.626 | INFO     | 125b725044da4d5382b992128394b22f | 成功认证Java用户: admin
2025-09-10 14:49:34.629 | INFO     | 125b725044da4d5382b992128394b22f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.530ms
2025-09-10 14:50:03.873 | INFO     | bc7dc46b8820457fa5047031d7fc29ce | 成功认证Java用户: admin
2025-09-10 14:50:03.875 | INFO     | bc7dc46b8820457fa5047031d7fc29ce | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.767ms
2025-09-10 14:50:04.636 | INFO     | 1458c2a325c14d29a9b9744a8d796879 | 成功认证Java用户: admin
2025-09-10 14:50:04.638 | INFO     | 1458c2a325c14d29a9b9744a8d796879 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.137ms
2025-09-10 14:50:04.641 | INFO     | 70bfba8ed48e431c99928b79fe7f7495 | 成功认证Java用户: admin
2025-09-10 14:50:04.644 | INFO     | 70bfba8ed48e431c99928b79fe7f7495 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.061ms
2025-09-10 14:50:34.636 | INFO     | 3675651307564b978b612d1bfeed2b61 | 成功认证Java用户: admin
2025-09-10 14:50:34.638 | INFO     | 3675651307564b978b612d1bfeed2b61 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.075ms
2025-09-10 14:50:34.640 | INFO     | 2f5a8b1997f7412f97efe606f575aab8 | 成功认证Java用户: admin
2025-09-10 14:50:34.641 | INFO     | 2f5a8b1997f7412f97efe606f575aab8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.128ms
2025-09-10 14:50:34.644 | INFO     | f96fa5a5bbe2444d8767b60287921e86 | 成功认证Java用户: admin
2025-09-10 14:50:34.645 | INFO     | f96fa5a5bbe2444d8767b60287921e86 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.327ms
2025-09-10 14:51:04.631 | INFO     | f01491e7fa984947b3f134171cfe927b | 成功认证Java用户: admin
2025-09-10 14:51:04.632 | INFO     | f01491e7fa984947b3f134171cfe927b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.426ms
2025-09-10 14:51:04.637 | INFO     | f072f626e4cc497aab09d0627508298d | 成功认证Java用户: admin
2025-09-10 14:51:04.638 | INFO     | f072f626e4cc497aab09d0627508298d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.610ms
2025-09-10 14:51:04.641 | INFO     | 7855662a3fb64cbe899c5fe00c91eb6b | 成功认证Java用户: admin
2025-09-10 14:51:04.644 | INFO     | 7855662a3fb64cbe899c5fe00c91eb6b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.202ms
2025-09-10 14:51:34.630 | INFO     | 9839398c96cc487f9eef6131b7b55034 | 成功认证Java用户: admin
2025-09-10 14:51:34.631 | INFO     | 9839398c96cc487f9eef6131b7b55034 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.001ms
2025-09-10 14:51:34.635 | INFO     | d3f21ddc5684412dbf6aad7bc18af4eb | 成功认证Java用户: admin
2025-09-10 14:51:34.636 | INFO     | d3f21ddc5684412dbf6aad7bc18af4eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.682ms
2025-09-10 14:52:04.629 | INFO     | d7f5e45722224fd583a39b3ead01c8cf | 成功认证Java用户: admin
2025-09-10 14:52:04.631 | INFO     | d7f5e45722224fd583a39b3ead01c8cf | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.525ms
2025-09-10 14:52:04.636 | INFO     | 505033200baf425c8dffb2709e73b183 | 成功认证Java用户: admin
2025-09-10 14:52:04.638 | INFO     | 505033200baf425c8dffb2709e73b183 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.916ms
2025-09-10 14:52:21.637 | INFO     | 4ea7c5ae0a894dcfb746cb2a53de34db | 成功认证Java用户: admin
2025-09-10 14:52:21.638 | INFO     | 4ea7c5ae0a894dcfb746cb2a53de34db | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.479ms
2025-09-10 14:52:34.634 | INFO     | bf7fb6e6f2f84c6081381f3001b4c589 | 成功认证Java用户: admin
2025-09-10 14:52:34.636 | INFO     | bf7fb6e6f2f84c6081381f3001b4c589 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.928ms
2025-09-10 14:52:34.641 | INFO     | ece77241a5a24eddbc7a23e0b114ea75 | 成功认证Java用户: admin
2025-09-10 14:52:34.643 | INFO     | ece77241a5a24eddbc7a23e0b114ea75 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.082ms
2025-09-10 14:53:04.630 | INFO     | 61ba4bef07984b37aba2aeebaf2b6583 | 成功认证Java用户: admin
2025-09-10 14:53:04.633 | INFO     | 61ba4bef07984b37aba2aeebaf2b6583 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.020ms
2025-09-10 14:53:21.639 | INFO     | 1d97364fd71e4aec9844a7e7c9630daa | 成功认证Java用户: admin
2025-09-10 14:53:21.643 | INFO     | 1d97364fd71e4aec9844a7e7c9630daa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.713ms
2025-09-10 14:53:21.649 | INFO     | de6740d619c7418e80e3549cfdd0dbb8 | 成功认证Java用户: admin
2025-09-10 14:53:21.653 | INFO     | de6740d619c7418e80e3549cfdd0dbb8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.239ms
2025-09-10 14:53:22.751 | INFO     | 4bcaaac018654270b637c497010f3cd7 | 成功认证Java用户: admin
2025-09-10 14:53:22.754 | INFO     | 4bcaaac018654270b637c497010f3cd7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.417ms
2025-09-10 14:53:53.630 | INFO     | 862c92ae08864b2289ad38e187b67ab1 | 成功认证Java用户: admin
2025-09-10 14:53:53.633 | INFO     | 862c92ae08864b2289ad38e187b67ab1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.331ms
2025-09-10 14:53:56.932 | INFO     | 35f3b85bac624d0d9ff559b66bcf7e35 | 成功认证Java用户: admin
2025-09-10 14:53:56.935 | INFO     | 35f3b85bac624d0d9ff559b66bcf7e35 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.513ms
2025-09-10 14:54:13.105 | INFO     | 2d94df43e6b344939fe84f1f8a7ccade | 成功认证Java用户: admin
2025-09-10 14:54:13.108 | INFO     | 2d94df43e6b344939fe84f1f8a7ccade | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.567ms
2025-09-10 14:54:21.631 | INFO     | 8e395a9fd0d74831a4e1a074446482a0 | 成功认证Java用户: admin
2025-09-10 14:54:21.633 | INFO     | 8e395a9fd0d74831a4e1a074446482a0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.213ms
2025-09-10 14:54:21.637 | INFO     | 709cd0e8fccc496fb72bab93a59aa34b | 成功认证Java用户: admin
2025-09-10 14:54:21.639 | INFO     | 709cd0e8fccc496fb72bab93a59aa34b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.004ms
2025-09-10 14:54:21.642 | INFO     | 786e89e2e34b4089b502a57f26f6ba02 | 成功认证Java用户: admin
2025-09-10 14:54:21.643 | INFO     | 786e89e2e34b4089b502a57f26f6ba02 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.785ms
2025-09-10 14:54:23.624 | INFO     | 17a3796e21184e0e91d873023ab174e8 | 成功认证Java用户: admin
2025-09-10 14:54:23.626 | INFO     | 17a3796e21184e0e91d873023ab174e8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.074ms
2025-09-10 14:54:27.638 | INFO     | f03aaebdb7864875aa677be50767e6c2 | 成功认证Java用户: admin
2025-09-10 14:54:27.640 | INFO     | f03aaebdb7864875aa677be50767e6c2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.501ms
2025-09-10 14:54:40.688 | INFO     | 87314085bae54388a98c57f7751076c2 | 成功认证Java用户: admin
2025-09-10 14:54:40.690 | INFO     | 87314085bae54388a98c57f7751076c2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.751ms
2025-09-10 14:54:43.637 | INFO     | 6940b8c1f8b64114a8b88367cc8e6656 | 成功认证Java用户: admin
2025-09-10 14:54:43.639 | INFO     | 6940b8c1f8b64114a8b88367cc8e6656 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.641ms
2025-09-10 14:54:53.638 | INFO     | aefbbd8078dc456dbb1e03a486a282a2 | 成功认证Java用户: admin
2025-09-10 14:54:53.641 | INFO     | aefbbd8078dc456dbb1e03a486a282a2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.784ms
2025-09-10 14:54:57.628 | INFO     | 29f9dd84a8ff466593ff601972e9293d | 成功认证Java用户: admin
2025-09-10 14:54:57.630 | INFO     | 29f9dd84a8ff466593ff601972e9293d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.155ms
2025-09-10 14:55:11.638 | INFO     | 7ac380dcfb434a7e847bdfa18b8949fa | 成功认证Java用户: admin
2025-09-10 14:55:11.640 | INFO     | 7ac380dcfb434a7e847bdfa18b8949fa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.822ms
2025-09-10 14:55:13.631 | INFO     | 29a665e54e9040208201ecca57f5a309 | 成功认证Java用户: admin
2025-09-10 14:55:13.633 | INFO     | 29a665e54e9040208201ecca57f5a309 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.910ms
2025-09-10 14:55:21.632 | INFO     | 03e7295c82fa4794870670978b57859f | 成功认证Java用户: admin
2025-09-10 14:55:21.634 | INFO     | 03e7295c82fa4794870670978b57859f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.238ms
2025-09-10 14:55:21.637 | INFO     | b7add0ede1a94e0fa1a012208e3809a9 | 成功认证Java用户: admin
2025-09-10 14:55:21.639 | INFO     | b7add0ede1a94e0fa1a012208e3809a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.806ms
2025-09-10 14:55:21.641 | INFO     | c34032f7e9ca463481d4c683d3670891 | 成功认证Java用户: admin
2025-09-10 14:55:21.642 | INFO     | c34032f7e9ca463481d4c683d3670891 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.716ms
2025-09-10 14:55:23.632 | INFO     | 4caed743a8c2464b976c236dfd427211 | 成功认证Java用户: admin
2025-09-10 14:55:23.633 | INFO     | 4caed743a8c2464b976c236dfd427211 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.725ms
2025-09-10 14:55:27.632 | INFO     | b37cee8e263f468ead97e70244b15d16 | 成功认证Java用户: admin
2025-09-10 14:55:27.634 | INFO     | b37cee8e263f468ead97e70244b15d16 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.397ms
2025-09-10 14:55:34.961 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 14:55:41.642 | INFO     | 9e4d5e524b324e4bbe1b4e0b4d79fbf3 | 成功认证Java用户: admin
2025-09-10 14:55:41.646 | INFO     | 9e4d5e524b324e4bbe1b4e0b4d79fbf3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 11.596ms
2025-09-10 14:55:43.638 | INFO     | 08a5b2e3c6bb408ba79bf5d5849804a1 | 成功认证Java用户: admin
2025-09-10 14:55:43.640 | INFO     | 08a5b2e3c6bb408ba79bf5d5849804a1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.406ms
2025-09-10 14:55:47.746 | INFO     | 23b93ecbf0ef4765acbb71f3e4ebf330 | 成功认证Java用户: admin
2025-09-10 14:55:47.748 | INFO     | 23b93ecbf0ef4765acbb71f3e4ebf330 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.960ms
2025-09-10 14:55:47.753 | INFO     | 84924ec08665453fbe282fc547d2bdcd | 成功认证Java用户: admin
2025-09-10 14:55:47.755 | INFO     | 84924ec08665453fbe282fc547d2bdcd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.143ms
2025-09-10 14:55:47.758 | INFO     | 49641a4c726e4dcd860d0966761d6873 | 成功认证Java用户: admin
2025-09-10 14:55:47.762 | INFO     | 49641a4c726e4dcd860d0966761d6873 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.324ms
2025-09-10 14:55:58.566 | INFO     | 0de0d4978769471ebb728e4b5a51981d | 成功认证Java用户: admin
2025-09-10 14:55:58.568 | INFO     | 0de0d4978769471ebb728e4b5a51981d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.500ms
2025-09-10 14:56:04.338 | INFO     | c9fa6d27268045d2bc462047d5837b83 | 成功认证Java用户: admin
2025-09-10 14:56:04.342 | INFO     | c9fa6d27268045d2bc462047d5837b83 | 用户 1 发起统一流式聊天
2025-09-10 14:56:04.344 | INFO     | c9fa6d27268045d2bc462047d5837b83 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 6.095ms
2025-09-10 14:56:05.361 | INFO     | c9fa6d27268045d2bc462047d5837b83 | 初始化LangGraph智能体服务...
2025-09-10 14:56:05.361 | INFO     | c9fa6d27268045d2bc462047d5837b83 | 加载了 3 个工具
2025-09-10 14:56:05.366 | INFO     | c9fa6d27268045d2bc462047d5837b83 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 14:56:05.366 | INFO     | c9fa6d27268045d2bc462047d5837b83 | LangGraph智能体服务初始化完成
2025-09-10 14:56:06.992 | INFO     | c9fa6d27268045d2bc462047d5837b83 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:56:13.276 | INFO     | c9fa6d27268045d2bc462047d5837b83 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 14:56:28.583 | INFO     | cf5de932abfe4c7d937dd0f30c93b632 | 成功认证Java用户: admin
2025-09-10 14:56:28.585 | INFO     | cf5de932abfe4c7d937dd0f30c93b632 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.149ms
2025-09-10 14:56:58.578 | INFO     | 54e7df4f7eea4768a05a0c99e024b42f | 成功认证Java用户: admin
2025-09-10 14:56:58.580 | INFO     | 54e7df4f7eea4768a05a0c99e024b42f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.237ms
2025-09-10 14:57:28.740 | INFO     | eeb3d7176a9a44808852d81a5a4dcda2 | 成功认证Java用户: admin
2025-09-10 14:57:28.743 | INFO     | eeb3d7176a9a44808852d81a5a4dcda2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.903ms
2025-09-10 14:57:58.634 | INFO     | 60f31e16f8f34f0faa8fce1bbd392f84 | 成功认证Java用户: admin
2025-09-10 14:57:58.637 | INFO     | 60f31e16f8f34f0faa8fce1bbd392f84 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.661ms
2025-09-10 14:58:28.640 | INFO     | 343d88ab72fc4c67a56a39ecfc7492fd | 成功认证Java用户: admin
2025-09-10 14:58:28.642 | INFO     | 343d88ab72fc4c67a56a39ecfc7492fd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.862ms
2025-09-10 14:58:58.636 | INFO     | 2ca7881bd07b4277aba1bab4944cca8b | 成功认证Java用户: admin
2025-09-10 14:58:58.638 | INFO     | 2ca7881bd07b4277aba1bab4944cca8b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.308ms
2025-09-10 15:00:18.613 | INFO     | 7d5f514b9a6741a79663f3037c07361c | 成功认证Java用户: admin
2025-09-10 15:00:18.616 | INFO     | 7d5f514b9a6741a79663f3037c07361c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.007ms
2025-09-10 15:00:28.579 | INFO     | 5a6edff50cd84f2eacd68fb69ea0e54d | 成功认证Java用户: admin
2025-09-10 15:00:28.581 | INFO     | 5a6edff50cd84f2eacd68fb69ea0e54d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.052ms
2025-09-10 15:00:58.633 | INFO     | 2c32556b91584cf0ad5521a40d5b5af8 | 成功认证Java用户: admin
2025-09-10 15:00:58.635 | INFO     | 2c32556b91584cf0ad5521a40d5b5af8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.484ms
2025-09-10 15:01:28.629 | INFO     | 98d09a57e98c401f894826e287d96e25 | 成功认证Java用户: admin
2025-09-10 15:01:28.631 | INFO     | 98d09a57e98c401f894826e287d96e25 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.901ms
2025-09-10 15:01:58.636 | INFO     | 8e5ba90776f2404daa3cd7b23facaa0a | 成功认证Java用户: admin
2025-09-10 15:01:58.640 | INFO     | 8e5ba90776f2404daa3cd7b23facaa0a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.717ms
2025-09-10 15:02:28.625 | INFO     | 7aec747006d44a7484bf713bc05957aa | 成功认证Java用户: admin
2025-09-10 15:02:28.627 | INFO     | 7aec747006d44a7484bf713bc05957aa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.238ms
2025-09-10 15:03:21.640 | INFO     | c8af53d6ba12472688848f1fe83af690 | 成功认证Java用户: admin
2025-09-10 15:03:21.644 | INFO     | c8af53d6ba12472688848f1fe83af690 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.674ms
2025-09-10 15:03:30.302 | INFO     | 3817c21537364e9cb33d93a71c0410e4 | 成功认证Java用户: admin
2025-09-10 15:03:30.304 | INFO     | 3817c21537364e9cb33d93a71c0410e4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.370ms
2025-09-10 15:04:00.638 | INFO     | 1f058d014cd2439e9501d9ed274f6240 | 成功认证Java用户: admin
2025-09-10 15:04:00.640 | INFO     | 1f058d014cd2439e9501d9ed274f6240 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.751ms
2025-09-10 15:04:21.637 | INFO     | 6fa81e01b6d545ba9bae3db7643220b4 | 成功认证Java用户: admin
2025-09-10 15:04:21.640 | INFO     | 6fa81e01b6d545ba9bae3db7643220b4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.414ms
2025-09-10 15:04:30.627 | INFO     | d218eb560d7e441a9f070967407cc57a | 成功认证Java用户: admin
2025-09-10 15:04:30.629 | INFO     | d218eb560d7e441a9f070967407cc57a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.020ms
2025-09-10 15:04:43.335 | INFO     | 047417de7d8e486cbb864b9b84a9e97e | 成功认证Java用户: admin
2025-09-10 15:04:43.338 | INFO     | 047417de7d8e486cbb864b9b84a9e97e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.916ms
2025-09-10 15:05:00.640 | INFO     | e0cea2d5741e480e88bec164a671cd0f | 成功认证Java用户: admin
2025-09-10 15:05:00.644 | INFO     | e0cea2d5741e480e88bec164a671cd0f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.762ms
2025-09-10 15:05:07.988 | INFO     | 90b340fd423d4456b9976860d3f9a1a4 | 成功认证Java用户: admin
2025-09-10 15:05:07.991 | INFO     | 90b340fd423d4456b9976860d3f9a1a4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.092ms
2025-09-10 15:05:13.637 | INFO     | c373440074314b74ae5c5d2745945dbd | 成功认证Java用户: admin
2025-09-10 15:05:13.638 | INFO     | c373440074314b74ae5c5d2745945dbd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.998ms
2025-09-10 15:05:21.643 | INFO     | c8b28bd3d97b4637a7cf204f5c6f7b96 | 成功认证Java用户: admin
2025-09-10 15:05:21.645 | INFO     | c8b28bd3d97b4637a7cf204f5c6f7b96 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.320ms
2025-09-10 15:05:30.632 | INFO     | 5c62f670a72e48dba07a6f48a14a51b1 | 成功认证Java用户: admin
2025-09-10 15:05:30.634 | INFO     | 5c62f670a72e48dba07a6f48a14a51b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.453ms
2025-09-10 15:05:38.635 | INFO     | ccff718229e84f91946c69e53ed546ae | 成功认证Java用户: admin
2025-09-10 15:05:38.637 | INFO     | ccff718229e84f91946c69e53ed546ae | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.772ms
2025-09-10 15:05:43.626 | INFO     | 73297f27886b4966b3b1adfec987c594 | 成功认证Java用户: admin
2025-09-10 15:05:43.628 | INFO     | 73297f27886b4966b3b1adfec987c594 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.754ms
2025-09-10 15:06:00.628 | INFO     | 79a8bb36001f4550b35a9f531d80930e | 成功认证Java用户: admin
2025-09-10 15:06:00.630 | INFO     | 79a8bb36001f4550b35a9f531d80930e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.070ms
2025-09-10 15:06:08.639 | INFO     | 84c5df549a6a46bdadfc1c60d15cfe5a | 成功认证Java用户: admin
2025-09-10 15:06:08.641 | INFO     | 84c5df549a6a46bdadfc1c60d15cfe5a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.392ms
2025-09-10 15:06:13.637 | INFO     | 8c29e449ac684c4ca2362ccb2a8fb4aa | 成功认证Java用户: admin
2025-09-10 15:06:13.640 | INFO     | 8c29e449ac684c4ca2362ccb2a8fb4aa | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.338ms
2025-09-10 15:06:21.636 | INFO     | d1f6f5ae7cb641d58d480fb730ab6864 | 成功认证Java用户: admin
2025-09-10 15:06:21.639 | INFO     | d1f6f5ae7cb641d58d480fb730ab6864 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.242ms
2025-09-10 15:06:30.629 | INFO     | e1e7fc88ae6b4f44a82479e781d47ca1 | 成功认证Java用户: admin
2025-09-10 15:06:30.631 | INFO     | e1e7fc88ae6b4f44a82479e781d47ca1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.436ms
2025-09-10 15:06:38.627 | INFO     | ac0ae8f7bbf34b8bb11976aa393c995a | 成功认证Java用户: admin
2025-09-10 15:06:38.629 | INFO     | ac0ae8f7bbf34b8bb11976aa393c995a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.606ms
2025-09-10 15:06:43.629 | INFO     | 41316d00545548c997fa8814d3a43af8 | 成功认证Java用户: admin
2025-09-10 15:06:43.630 | INFO     | 41316d00545548c997fa8814d3a43af8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.509ms
2025-09-10 15:07:08.635 | INFO     | 23d1363c2cd54931854656d410afffeb | 成功认证Java用户: admin
2025-09-10 15:07:08.638 | INFO     | 23d1363c2cd54931854656d410afffeb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.816ms
2025-09-10 15:07:13.637 | INFO     | aaf0353771364d75881114278a4ee793 | 成功认证Java用户: admin
2025-09-10 15:07:13.639 | INFO     | aaf0353771364d75881114278a4ee793 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.088ms
2025-09-10 15:07:21.629 | INFO     | 7a53553c6a904ac6ac0240a880158fd3 | 成功认证Java用户: admin
2025-09-10 15:07:21.631 | INFO     | 7a53553c6a904ac6ac0240a880158fd3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.255ms
2025-09-10 15:07:21.634 | INFO     | cf7962cc1ed846f389ea624da5c67516 | 成功认证Java用户: admin
2025-09-10 15:07:21.635 | INFO     | cf7962cc1ed846f389ea624da5c67516 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.538ms
2025-09-10 15:07:28.082 | INFO     | f8942587b79f48f8a1fde6f19e62835b | 成功认证Java用户: admin
2025-09-10 15:07:28.083 | INFO     | f8942587b79f48f8a1fde6f19e62835b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.979ms
2025-09-10 15:07:38.635 | INFO     | 25e6c8b8252040cd8d3f77dba7616bd7 | 成功认证Java用户: admin
2025-09-10 15:07:38.637 | INFO     | 25e6c8b8252040cd8d3f77dba7616bd7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.721ms
2025-09-10 15:07:43.630 | INFO     | fbc7f4b0ec9840d7bf799a2446602b00 | 成功认证Java用户: admin
2025-09-10 15:07:43.632 | INFO     | fbc7f4b0ec9840d7bf799a2446602b00 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.212ms
2025-09-10 15:07:47.361 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 15:07:58.647 | INFO     | 1cd13c271cc14b24870be7551ce6de54 | 成功认证Java用户: admin
2025-09-10 15:07:58.650 | INFO     | 1cd13c271cc14b24870be7551ce6de54 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 14.053ms
2025-09-10 15:08:07.615 | INFO     | d343d89e559e4379a2fab82c088651a9 | 成功认证Java用户: admin
2025-09-10 15:08:07.618 | INFO     | d343d89e559e4379a2fab82c088651a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.848ms
2025-09-10 15:08:07.621 | INFO     | 6c1c8ebdd1ea4c07a8e85c300f891966 | 成功认证Java用户: admin
2025-09-10 15:08:07.623 | INFO     | 6c1c8ebdd1ea4c07a8e85c300f891966 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.195ms
2025-09-10 15:08:07.999 | INFO     | 8b19ccbd925e4c2884e11833c332301c | 成功认证Java用户: admin
2025-09-10 15:08:08.000 | INFO     | 8b19ccbd925e4c2884e11833c332301c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.312ms
2025-09-10 15:08:16.174 | INFO     | e296b34e39ce4273a457f8807d9314d1 | 成功认证Java用户: admin
2025-09-10 15:08:16.176 | INFO     | e296b34e39ce4273a457f8807d9314d1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.337ms
2025-09-10 15:08:21.221 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | 成功认证Java用户: admin
2025-09-10 15:08:21.226 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | 用户 1 发起统一流式聊天
2025-09-10 15:08:21.228 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 8.468ms
2025-09-10 15:08:22.235 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | 初始化LangGraph智能体服务...
2025-09-10 15:08:22.235 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | 加载了 3 个工具
2025-09-10 15:08:22.241 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:08:22.241 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | LangGraph智能体服务初始化完成
2025-09-10 15:08:46.186 | INFO     | e21780d06055416d963b8276651636ac | 成功认证Java用户: admin
2025-09-10 15:08:46.189 | INFO     | e21780d06055416d963b8276651636ac | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.833ms
2025-09-10 15:08:54.387 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 15:08:54.388 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | Retrying request to /chat/completions in 0.494238 seconds
2025-09-10 15:09:16.188 | INFO     | 09f276b9b20f4e34ad4158322d9188f3 | 成功认证Java用户: admin
2025-09-10 15:09:16.190 | INFO     | 09f276b9b20f4e34ad4158322d9188f3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.473ms
2025-09-10 15:09:25.272 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 15:09:25.273 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | Retrying request to /chat/completions in 0.905580 seconds
2025-09-10 15:09:32.079 | INFO     | 027db43824a14e1a9c433dfcc6ceac54 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-09-10 15:09:46.194 | INFO     | ec30ef8e1c6d4dbfb5faf60037f98a18 | 成功认证Java用户: admin
2025-09-10 15:09:46.196 | INFO     | ec30ef8e1c6d4dbfb5faf60037f98a18 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.396ms
2025-09-10 15:10:16.190 | INFO     | 1a0130fcd5ce4f4d942f5c011c25b6e0 | 成功认证Java用户: admin
2025-09-10 15:10:16.191 | INFO     | 1a0130fcd5ce4f4d942f5c011c25b6e0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.306ms
2025-09-10 15:10:34.749 | INFO     | 4d854cfef84041eb957ffb5baaaff5ee | 成功认证Java用户: admin
2025-09-10 15:10:34.751 | INFO     | 4d854cfef84041eb957ffb5baaaff5ee | 已清空用户 1 的会话 chat_session_1757488096161_mykb2q59h2 历史记录
2025-09-10 15:10:34.752 | INFO     | 4d854cfef84041eb957ffb5baaaff5ee | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757488096161_mykb2q59h2 | 4.166ms
2025-09-10 15:10:49.697 | INFO     | 8a74112245b245449ccd33a59a0208a9 | 成功认证Java用户: admin
2025-09-10 15:10:49.700 | INFO     | 8a74112245b245449ccd33a59a0208a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.985ms
2025-09-10 15:10:54.112 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | 成功认证Java用户: admin
2025-09-10 15:10:54.114 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | 用户 1 发起统一流式聊天
2025-09-10 15:10:54.115 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.599ms
2025-09-10 15:10:55.147 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | 初始化LangGraph智能体服务...
2025-09-10 15:10:55.147 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | 加载了 3 个工具
2025-09-10 15:10:55.151 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:10:55.151 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | LangGraph智能体服务初始化完成
2025-09-10 15:10:55.180 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:10:59.025 | INFO     | 810a9f59e7ae4be6aeddd6186078a4f8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:11:19.712 | INFO     | 162580f7fed14b2380f5070907a97a06 | 成功认证Java用户: admin
2025-09-10 15:11:19.714 | INFO     | 162580f7fed14b2380f5070907a97a06 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.321ms
2025-09-10 15:11:49.714 | INFO     | 36955380f16d41e99dd09b32b01e3c95 | 成功认证Java用户: admin
2025-09-10 15:11:49.716 | INFO     | 36955380f16d41e99dd09b32b01e3c95 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.804ms
2025-09-10 15:12:20.626 | INFO     | e6213fd612c94582872d323ef6c15923 | 成功认证Java用户: admin
2025-09-10 15:12:20.628 | INFO     | e6213fd612c94582872d323ef6c15923 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.639ms
2025-09-10 15:12:36.784 | INFO     | 43c96910237d4473916e6d1fac42f1d2 | 成功认证Java用户: admin
2025-09-10 15:12:36.785 | INFO     | 43c96910237d4473916e6d1fac42f1d2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.107ms
2025-09-10 15:12:50.635 | INFO     | 9c9d6ef82c4e4589aa51835113d9fbda | 成功认证Java用户: admin
2025-09-10 15:12:50.636 | INFO     | 9c9d6ef82c4e4589aa51835113d9fbda | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.636ms
2025-09-10 15:13:07.635 | INFO     | 0e0f463fc647405ca495bdf60f749526 | 成功认证Java用户: admin
2025-09-10 15:13:07.637 | INFO     | 0e0f463fc647405ca495bdf60f749526 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.632ms
2025-09-10 15:13:20.638 | INFO     | 0767a54d43e442eca53b523710cb9c24 | 成功认证Java用户: admin
2025-09-10 15:13:20.641 | INFO     | 0767a54d43e442eca53b523710cb9c24 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.462ms
2025-09-10 15:13:37.635 | INFO     | b8b56a94e733423cbcbb336f50704c7c | 成功认证Java用户: admin
2025-09-10 15:13:37.637 | INFO     | b8b56a94e733423cbcbb336f50704c7c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.461ms
2025-09-10 15:13:50.629 | INFO     | 90bef6fa8243445b891dda0b0de108bf | 成功认证Java用户: admin
2025-09-10 15:13:50.631 | INFO     | 90bef6fa8243445b891dda0b0de108bf | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.035ms
2025-09-10 15:14:07.638 | INFO     | fd0ad123613c4a3c89c23eced485376f | 成功认证Java用户: admin
2025-09-10 15:14:07.642 | INFO     | fd0ad123613c4a3c89c23eced485376f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.570ms
2025-09-10 15:14:21.636 | INFO     | 4cb5483329a940ae88c18a12d3e443a8 | 成功认证Java用户: admin
2025-09-10 15:14:21.638 | INFO     | 4cb5483329a940ae88c18a12d3e443a8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.706ms
2025-09-10 15:14:37.632 | INFO     | 85d12d73e6a347cd92ecf604ffdbde9c | 成功认证Java用户: admin
2025-09-10 15:14:37.635 | INFO     | 85d12d73e6a347cd92ecf604ffdbde9c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.996ms
2025-09-10 15:15:07.636 | INFO     | 9d140b3d8fc54c3d816e310fe01c57bd | 成功认证Java用户: admin
2025-09-10 15:15:07.639 | INFO     | 9d140b3d8fc54c3d816e310fe01c57bd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.494ms
2025-09-10 15:15:21.632 | INFO     | 490f57f2c1124ec2882bc18bb234a485 | 成功认证Java用户: admin
2025-09-10 15:15:21.634 | INFO     | 490f57f2c1124ec2882bc18bb234a485 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.432ms
2025-09-10 15:15:25.099 | INFO     | d28732d39a80463fa194097de770bde0 | 成功认证Java用户: admin
2025-09-10 15:15:25.100 | INFO     | d28732d39a80463fa194097de770bde0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.928ms
2025-09-10 15:15:37.629 | INFO     | b47ba02ec8b4408ba5ef473505969e9b | 成功认证Java用户: admin
2025-09-10 15:15:37.630 | INFO     | b47ba02ec8b4408ba5ef473505969e9b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.312ms
2025-09-10 15:15:55.628 | INFO     | 8285b7aa8d944a9d9ab5a56713c2a14d | 成功认证Java用户: admin
2025-09-10 15:15:55.631 | INFO     | 8285b7aa8d944a9d9ab5a56713c2a14d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.415ms
2025-09-10 15:16:21.639 | INFO     | 0fa480f755404f13b26c3ab0dfdbf910 | 成功认证Java用户: admin
2025-09-10 15:16:21.642 | INFO     | 0fa480f755404f13b26c3ab0dfdbf910 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.810ms
2025-09-10 15:16:21.646 | INFO     | cae3bdf1af364ae1bf3b553ff7442363 | 成功认证Java用户: admin
2025-09-10 15:16:21.648 | INFO     | cae3bdf1af364ae1bf3b553ff7442363 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.810ms
2025-09-10 15:16:25.634 | INFO     | 5044dcd1c9694066ae6a1c35eacb0328 | 成功认证Java用户: admin
2025-09-10 15:16:25.636 | INFO     | 5044dcd1c9694066ae6a1c35eacb0328 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.092ms
2025-09-10 15:16:55.631 | INFO     | 22655bab42de42e8b92f08e46f21506d | 成功认证Java用户: admin
2025-09-10 15:16:55.633 | INFO     | 22655bab42de42e8b92f08e46f21506d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.958ms
2025-09-10 15:17:21.632 | INFO     | 792e5bd7fb9a43c9ba1b23e6f2888a8c | 成功认证Java用户: admin
2025-09-10 15:17:21.634 | INFO     | 792e5bd7fb9a43c9ba1b23e6f2888a8c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.060ms
2025-09-10 15:17:21.637 | INFO     | 7bdfb47c72a444698fe1c0bd7afc9f4e | 成功认证Java用户: admin
2025-09-10 15:17:21.638 | INFO     | 7bdfb47c72a444698fe1c0bd7afc9f4e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.676ms
2025-09-10 15:17:25.634 | INFO     | 774fadf7c80742dca22e749ec81d47bc | 成功认证Java用户: admin
2025-09-10 15:17:25.636 | INFO     | 774fadf7c80742dca22e749ec81d47bc | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.492ms
2025-09-10 15:17:55.635 | INFO     | 9f800a6114ba4766a784cf3dcda86d75 | 成功认证Java用户: admin
2025-09-10 15:17:55.637 | INFO     | 9f800a6114ba4766a784cf3dcda86d75 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.873ms
2025-09-10 15:17:55.848 | INFO     | dcfeab13f11940008b04a29c01f078c0 | 成功认证Java用户: admin
2025-09-10 15:17:55.850 | INFO     | dcfeab13f11940008b04a29c01f078c0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.414ms
2025-09-10 15:17:55.855 | INFO     | d1f385df9d9f4ce7a733e40eff9a1b4f | 成功认证Java用户: admin
2025-09-10 15:17:55.857 | INFO     | d1f385df9d9f4ce7a733e40eff9a1b4f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.413ms
2025-09-10 15:18:07.641 | INFO     | 9f941d7ad39c420eaa46351dd284216c | 成功认证Java用户: admin
2025-09-10 15:18:07.643 | INFO     | 9f941d7ad39c420eaa46351dd284216c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.948ms
2025-09-10 15:18:20.631 | INFO     | 5f382900afde4491bb839040a5a6d4d8 | 成功认证Java用户: admin
2025-09-10 15:18:20.633 | INFO     | 5f382900afde4491bb839040a5a6d4d8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.881ms
2025-09-10 15:18:25.630 | INFO     | ab39016f21854822bbd526c00e476e9d | 成功认证Java用户: admin
2025-09-10 15:18:25.632 | INFO     | ab39016f21854822bbd526c00e476e9d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.079ms
2025-09-10 15:18:37.630 | INFO     | 334e7881e31e46888d4cadb5111aa963 | 成功认证Java用户: admin
2025-09-10 15:18:37.632 | INFO     | 334e7881e31e46888d4cadb5111aa963 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.563ms
2025-09-10 15:18:50.634 | INFO     | b0ee56396a4549ddaee747195d4cbcfd | 成功认证Java用户: admin
2025-09-10 15:18:50.635 | INFO     | b0ee56396a4549ddaee747195d4cbcfd | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.082ms
2025-09-10 15:18:55.631 | INFO     | e658e49e7fff4960b5391e16fe1afd51 | 成功认证Java用户: admin
2025-09-10 15:18:55.633 | INFO     | e658e49e7fff4960b5391e16fe1afd51 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.171ms
2025-09-10 15:19:07.628 | INFO     | ba2c9bc27bf64dcbb03e8080af14b6d5 | 成功认证Java用户: admin
2025-09-10 15:19:07.629 | INFO     | ba2c9bc27bf64dcbb03e8080af14b6d5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.840ms
2025-09-10 15:19:21.629 | INFO     | 3cc7080ddfc84813bba800aa550970c6 | 成功认证Java用户: admin
2025-09-10 15:19:21.630 | INFO     | 3cc7080ddfc84813bba800aa550970c6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.228ms
2025-09-10 15:20:21.634 | INFO     | e45b82281e7e480996882eed37c84418 | 成功认证Java用户: admin
2025-09-10 15:20:21.637 | INFO     | e45b82281e7e480996882eed37c84418 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.972ms
2025-09-10 15:20:21.641 | INFO     | a7aa2fff794b4cbcbeec3fc2c851b7fc | 成功认证Java用户: admin
2025-09-10 15:20:21.643 | INFO     | a7aa2fff794b4cbcbeec3fc2c851b7fc | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.569ms
2025-09-10 15:20:21.646 | INFO     | ef9557090490483f863d8842c2ca24c8 | 成功认证Java用户: admin
2025-09-10 15:20:21.648 | INFO     | ef9557090490483f863d8842c2ca24c8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.891ms
2025-09-10 15:21:21.642 | INFO     | 64bfa752524c4cd4ae3d8998b9b55c96 | 成功认证Java用户: admin
2025-09-10 15:21:21.643 | INFO     | 64bfa752524c4cd4ae3d8998b9b55c96 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.383ms
2025-09-10 15:21:21.648 | INFO     | 666057fcf4594487bba39c409bddf6ca | 成功认证Java用户: admin
2025-09-10 15:21:21.650 | INFO     | 666057fcf4594487bba39c409bddf6ca | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.180ms
2025-09-10 15:21:21.653 | INFO     | 5d652df796bc4246a4bdcea00f105068 | 成功认证Java用户: admin
2025-09-10 15:21:21.655 | INFO     | 5d652df796bc4246a4bdcea00f105068 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.960ms
2025-09-10 15:22:21.643 | INFO     | 764e5a1ef1d64984a7acaa9076e08052 | 成功认证Java用户: admin
2025-09-10 15:22:21.645 | INFO     | 764e5a1ef1d64984a7acaa9076e08052 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.032ms
2025-09-10 15:22:21.650 | INFO     | 4775c4aa87a44d9f859d36833dacfde3 | 成功认证Java用户: admin
2025-09-10 15:22:21.651 | INFO     | 4775c4aa87a44d9f859d36833dacfde3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.410ms
2025-09-10 15:22:21.655 | INFO     | d95af6a8ec1049cf950c47187b0fb876 | 成功认证Java用户: admin
2025-09-10 15:22:21.657 | INFO     | d95af6a8ec1049cf950c47187b0fb876 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.935ms
2025-09-10 15:23:21.638 | INFO     | 0d988c7919cf4b27b935b3f389b0321d | 成功认证Java用户: admin
2025-09-10 15:23:21.640 | INFO     | 0d988c7919cf4b27b935b3f389b0321d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.260ms
2025-09-10 15:23:21.644 | INFO     | 7ff04e5d4c0047b8b231b170aeea6425 | 成功认证Java用户: admin
2025-09-10 15:23:21.646 | INFO     | 7ff04e5d4c0047b8b231b170aeea6425 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.284ms
2025-09-10 15:23:21.649 | INFO     | 8f9206c927d9407b884900ed51dcd01a | 成功认证Java用户: admin
2025-09-10 15:23:21.650 | INFO     | 8f9206c927d9407b884900ed51dcd01a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.930ms
2025-09-10 15:24:21.638 | INFO     | b324edb35b7d4ff18c3be9c4d25f3da4 | 成功认证Java用户: admin
2025-09-10 15:24:21.640 | INFO     | b324edb35b7d4ff18c3be9c4d25f3da4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.395ms
2025-09-10 15:24:21.645 | INFO     | 8dd0fd2c56354648b4e287803f013d52 | 成功认证Java用户: admin
2025-09-10 15:24:21.646 | INFO     | 8dd0fd2c56354648b4e287803f013d52 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.480ms
2025-09-10 15:24:21.649 | INFO     | 55069a4415a84c638a4bae1c44efa0a8 | 成功认证Java用户: admin
2025-09-10 15:24:21.651 | INFO     | 55069a4415a84c638a4bae1c44efa0a8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.201ms
2025-09-10 15:26:50.355 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 15:27:21.656 | INFO     | 3c7f00d546b54ad7aa031fd7bd43d15d | 成功认证Java用户: admin
2025-09-10 15:27:21.659 | INFO     | 3c7f00d546b54ad7aa031fd7bd43d15d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 16.239ms
2025-09-10 15:27:21.661 | INFO     | 4f5c52afed344ab49a477613f24a6e0e | 成功认证Java用户: admin
2025-09-10 15:27:21.663 | INFO     | 4f5c52afed344ab49a477613f24a6e0e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.674ms
2025-09-10 15:27:21.666 | INFO     | f581996ac97a4c5db766a1e27ad9258c | 成功认证Java用户: admin
2025-09-10 15:27:21.667 | INFO     | f581996ac97a4c5db766a1e27ad9258c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.589ms
2025-09-10 15:27:27.002 | INFO     | c31e6ab9ed884a7a85148ad17cef9344 | 成功认证Java用户: admin
2025-09-10 15:27:27.004 | INFO     | c31e6ab9ed884a7a85148ad17cef9344 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.879ms
2025-09-10 15:27:40.582 | INFO     | 90c42ecb48014f36b07158c7aea80f42 | 成功认证Java用户: admin
2025-09-10 15:27:40.585 | INFO     | 90c42ecb48014f36b07158c7aea80f42 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.852ms
2025-09-10 15:28:10.637 | INFO     | f4278c2b97d24be5bc46e405f9ed1104 | 成功认证Java用户: admin
2025-09-10 15:28:10.639 | INFO     | f4278c2b97d24be5bc46e405f9ed1104 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.802ms
2025-09-10 15:28:40.629 | INFO     | d4751b852e374e0fb59508931a930783 | 成功认证Java用户: admin
2025-09-10 15:28:40.631 | INFO     | d4751b852e374e0fb59508931a930783 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.105ms
2025-09-10 15:29:10.634 | INFO     | 453098502cf9492ba65a1841648e5c1b | 成功认证Java用户: admin
2025-09-10 15:29:10.636 | INFO     | 453098502cf9492ba65a1841648e5c1b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.962ms
2025-09-10 15:29:40.633 | INFO     | 3e2d94cb06984c0fa0909415d1682750 | 成功认证Java用户: admin
2025-09-10 15:29:40.635 | INFO     | 3e2d94cb06984c0fa0909415d1682750 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.865ms
2025-09-10 15:30:10.626 | INFO     | 3a02c3f37fec4feea1f48a88fc1864c6 | 成功认证Java用户: admin
2025-09-10 15:30:10.628 | INFO     | 3a02c3f37fec4feea1f48a88fc1864c6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.270ms
2025-09-10 15:30:40.628 | INFO     | 0749552df0c442a289568e5c5e940fab | 成功认证Java用户: admin
2025-09-10 15:30:40.629 | INFO     | 0749552df0c442a289568e5c5e940fab | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.018ms
2025-09-10 15:31:21.640 | INFO     | 9789120c2e5348d9a325bd6444831d89 | 成功认证Java用户: admin
2025-09-10 15:31:21.642 | INFO     | 9789120c2e5348d9a325bd6444831d89 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.452ms
2025-09-10 15:32:21.641 | INFO     | 7e49a0f4e95242448e4c68f0251b51fe | 成功认证Java用户: admin
2025-09-10 15:32:21.642 | INFO     | 7e49a0f4e95242448e4c68f0251b51fe | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.880ms
2025-09-10 15:32:50.474 | INFO     | 359bf29ed658415882fefc3d2f8f42bb | 成功认证Java用户: admin
2025-09-10 15:32:50.476 | INFO     | 359bf29ed658415882fefc3d2f8f42bb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.005ms
2025-09-10 15:33:16.052 | INFO     | 33ead7054d834b578d95102bab7d1431 | 成功认证Java用户: admin
2025-09-10 15:33:16.054 | INFO     | 33ead7054d834b578d95102bab7d1431 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.213ms
2025-09-10 15:33:20.634 | INFO     | fa755b1286784c2199b71974c6aac27b | 成功认证Java用户: admin
2025-09-10 15:33:20.636 | INFO     | fa755b1286784c2199b71974c6aac27b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.798ms
2025-09-10 15:33:21.637 | INFO     | fdd53934b6ac45d1a313e0c364405e88 | 成功认证Java用户: admin
2025-09-10 15:33:21.639 | INFO     | fdd53934b6ac45d1a313e0c364405e88 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.520ms
2025-09-10 15:33:34.800 | INFO     | e9ec5717eccd491c8cdc6a22fd35d719 | 成功认证Java用户: admin
2025-09-10 15:33:34.801 | INFO     | e9ec5717eccd491c8cdc6a22fd35d719 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.978ms
2025-09-10 15:33:46.631 | INFO     | 295555400f374eb98d4c0e4a3251618c | 成功认证Java用户: admin
2025-09-10 15:33:46.633 | INFO     | 295555400f374eb98d4c0e4a3251618c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.011ms
2025-09-10 15:33:50.636 | INFO     | e385b435b53e447c80dd61ec6e80aa0b | 成功认证Java用户: admin
2025-09-10 15:33:50.638 | INFO     | e385b435b53e447c80dd61ec6e80aa0b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.180ms
2025-09-10 15:34:05.631 | INFO     | c75d4c66ecbe4b4e815547a0a70e7eb7 | 成功认证Java用户: admin
2025-09-10 15:34:05.632 | INFO     | c75d4c66ecbe4b4e815547a0a70e7eb7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.319ms
2025-09-10 15:34:16.624 | INFO     | 26a187a4b66e4fbabf596838668fa585 | 成功认证Java用户: admin
2025-09-10 15:34:16.626 | INFO     | 26a187a4b66e4fbabf596838668fa585 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.378ms
2025-09-10 15:34:20.635 | INFO     | 97b059084fae419fb4a6505bfa19a7ec | 成功认证Java用户: admin
2025-09-10 15:34:20.636 | INFO     | 97b059084fae419fb4a6505bfa19a7ec | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.645ms
2025-09-10 15:34:21.640 | INFO     | 15c0ce46f8384c0292da2cff83f0f19e | 成功认证Java用户: admin
2025-09-10 15:34:21.642 | INFO     | 15c0ce46f8384c0292da2cff83f0f19e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.821ms
2025-09-10 15:34:35.630 | INFO     | 6b3e5460e69d44d9afde3a2348aa19c1 | 成功认证Java用户: admin
2025-09-10 15:34:35.631 | INFO     | 6b3e5460e69d44d9afde3a2348aa19c1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.786ms
2025-09-10 15:34:47.553 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 15:34:50.643 | INFO     | 48e80ac7cdd043b09e684f7110ad9cc0 | 成功认证Java用户: admin
2025-09-10 15:34:50.646 | INFO     | 48e80ac7cdd043b09e684f7110ad9cc0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 9.307ms
2025-09-10 15:35:05.625 | INFO     | f9374e72e409424d97df4727bab5b602 | 成功认证Java用户: admin
2025-09-10 15:35:05.627 | INFO     | f9374e72e409424d97df4727bab5b602 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.768ms
2025-09-10 15:35:16.632 | INFO     | 789a8bdf891f419db9cffb62a80e159c | 成功认证Java用户: admin
2025-09-10 15:35:16.634 | INFO     | 789a8bdf891f419db9cffb62a80e159c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.473ms
2025-09-10 15:35:20.634 | INFO     | d570c3342b3b432981a1a3db323efd0e | 成功认证Java用户: admin
2025-09-10 15:35:20.636 | INFO     | d570c3342b3b432981a1a3db323efd0e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.129ms
2025-09-10 15:35:21.638 | INFO     | ade8671384f04c01b629ea6a80e77033 | 成功认证Java用户: admin
2025-09-10 15:35:21.640 | INFO     | ade8671384f04c01b629ea6a80e77033 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.475ms
2025-09-10 15:35:35.627 | INFO     | 44224696ce29447b81f36963c7486fb7 | 成功认证Java用户: admin
2025-09-10 15:35:35.629 | INFO     | 44224696ce29447b81f36963c7486fb7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.970ms
2025-09-10 15:35:46.624 | INFO     | 8144d7e132bd45a4a8a752d631518d42 | 成功认证Java用户: admin
2025-09-10 15:35:46.626 | INFO     | 8144d7e132bd45a4a8a752d631518d42 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.933ms
2025-09-10 15:35:50.631 | INFO     | 8985030eb65c4f1e9af470015491755f | 成功认证Java用户: admin
2025-09-10 15:35:50.633 | INFO     | 8985030eb65c4f1e9af470015491755f | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.289ms
2025-09-10 15:36:00.249 | INFO     | a8b0e5fec6c445a8b949f7f3a6c517a2 | 成功认证Java用户: admin
2025-09-10 15:36:00.253 | INFO     | a8b0e5fec6c445a8b949f7f3a6c517a2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 5.753ms
2025-09-10 15:36:12.587 | INFO     | e8f53c1fce5146838bfd061255deaf14 | 成功认证Java用户: admin
2025-09-10 15:36:12.589 | INFO     | e8f53c1fce5146838bfd061255deaf14 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.413ms
2025-09-10 15:36:24.068 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | 成功认证Java用户: admin
2025-09-10 15:36:24.073 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | 用户 1 发起统一流式聊天
2025-09-10 15:36:24.074 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 7.500ms
2025-09-10 15:36:25.068 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | 初始化LangGraph智能体服务...
2025-09-10 15:36:25.068 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | 加载了 3 个工具
2025-09-10 15:36:25.084 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:36:25.084 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | LangGraph智能体服务初始化完成
2025-09-10 15:36:26.977 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:36:33.909 | INFO     | 91f87b1df0d346fdb4ba72966ffa3c50 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:36:42.599 | INFO     | 2999a50092834465b1b206f7d142117b | 成功认证Java用户: admin
2025-09-10 15:36:42.602 | INFO     | 2999a50092834465b1b206f7d142117b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.698ms
2025-09-10 15:37:12.626 | INFO     | 1616fd76841b458980e17b65cad1df85 | 成功认证Java用户: admin
2025-09-10 15:37:12.627 | INFO     | 1616fd76841b458980e17b65cad1df85 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.699ms
2025-09-10 15:37:42.628 | INFO     | db20ae76e15b416cb7dd6892eff7ce8b | 成功认证Java用户: admin
2025-09-10 15:37:42.630 | INFO     | db20ae76e15b416cb7dd6892eff7ce8b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.124ms
2025-09-10 15:38:12.630 | INFO     | 0fdd7f4642294d3793dee30f1542972a | 成功认证Java用户: admin
2025-09-10 15:38:12.632 | INFO     | 0fdd7f4642294d3793dee30f1542972a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.562ms
2025-09-10 15:38:42.634 | INFO     | e774e910b21f40e3be8602c409dfc567 | 成功认证Java用户: admin
2025-09-10 15:38:42.636 | INFO     | e774e910b21f40e3be8602c409dfc567 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.057ms
2025-09-10 15:39:12.637 | INFO     | e53accaf429049e596dfb676e9ca89c0 | 成功认证Java用户: admin
2025-09-10 15:39:12.639 | INFO     | e53accaf429049e596dfb676e9ca89c0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.959ms
2025-09-10 15:39:42.599 | INFO     | 1d13fc5cfa6f41fea0a9809fc557a769 | 成功认证Java用户: admin
2025-09-10 15:39:42.600 | INFO     | 1d13fc5cfa6f41fea0a9809fc557a769 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.943ms
2025-09-10 15:40:12.632 | INFO     | cf6cf2363fb4426599ac56700e1c7ef2 | 成功认证Java用户: admin
2025-09-10 15:40:12.633 | INFO     | cf6cf2363fb4426599ac56700e1c7ef2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.330ms
2025-09-10 15:40:42.627 | INFO     | 8431e89f4b10451d8744ac1f5ed810ca | 成功认证Java用户: admin
2025-09-10 15:40:42.629 | INFO     | 8431e89f4b10451d8744ac1f5ed810ca | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.882ms
2025-09-10 15:41:21.627 | INFO     | e3eae2edc7834be7b16ba5ed2de7a4c4 | 成功认证Java用户: admin
2025-09-10 15:41:21.628 | INFO     | e3eae2edc7834be7b16ba5ed2de7a4c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.386ms
2025-09-10 15:42:21.635 | INFO     | 1f4feda4b3d142d7b74e94834246b901 | 成功认证Java用户: admin
2025-09-10 15:42:21.636 | INFO     | 1f4feda4b3d142d7b74e94834246b901 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.095ms
2025-09-10 15:42:42.253 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 15:42:49.481 | INFO     | 059220b0b617471b83700dbf196fc328 | 成功认证Java用户: admin
2025-09-10 15:42:49.484 | INFO     | 059220b0b617471b83700dbf196fc328 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 15.172ms
2025-09-10 15:42:57.654 | INFO     | a510fd0312bd4de4a849be4f6fee6220 | 成功认证Java用户: admin
2025-09-10 15:42:57.656 | INFO     | a510fd0312bd4de4a849be4f6fee6220 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.499ms
2025-09-10 15:43:03.041 | INFO     | 7ec46009391949cdafb24c35280ec300 | 成功认证Java用户: admin
2025-09-10 15:43:03.045 | INFO     | 7ec46009391949cdafb24c35280ec300 | 用户 1 发起统一流式聊天
2025-09-10 15:43:03.046 | INFO     | 7ec46009391949cdafb24c35280ec300 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 5.480ms
2025-09-10 15:43:04.079 | INFO     | 7ec46009391949cdafb24c35280ec300 | 初始化LangGraph智能体服务...
2025-09-10 15:43:04.080 | INFO     | 7ec46009391949cdafb24c35280ec300 | 加载了 3 个工具
2025-09-10 15:43:04.084 | INFO     | 7ec46009391949cdafb24c35280ec300 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:43:04.084 | INFO     | 7ec46009391949cdafb24c35280ec300 | LangGraph智能体服务初始化完成
2025-09-10 15:43:05.728 | INFO     | 7ec46009391949cdafb24c35280ec300 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:43:12.301 | INFO     | 7ec46009391949cdafb24c35280ec300 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:43:27.667 | INFO     | 2dec03024a2443cbac166afa956a8a7b | 成功认证Java用户: admin
2025-09-10 15:43:27.669 | INFO     | 2dec03024a2443cbac166afa956a8a7b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.406ms
2025-09-10 15:43:58.628 | INFO     | 8af8d2b18fb84899a495517bbbc423df | 成功认证Java用户: admin
2025-09-10 15:43:58.630 | INFO     | 8af8d2b18fb84899a495517bbbc423df | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.436ms
2025-09-10 15:44:28.633 | INFO     | 82c7ea0dd46748a6917be3fbcce80dd2 | 成功认证Java用户: admin
2025-09-10 15:44:28.635 | INFO     | 82c7ea0dd46748a6917be3fbcce80dd2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.278ms
2025-09-10 15:44:58.627 | INFO     | f92f479ecdd54907bf9b0a798cb26ccb | 成功认证Java用户: admin
2025-09-10 15:44:58.629 | INFO     | f92f479ecdd54907bf9b0a798cb26ccb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.238ms
2025-09-10 15:45:28.639 | INFO     | f2b1f3b524e14be9873a494918b295ad | 成功认证Java用户: admin
2025-09-10 15:45:28.641 | INFO     | f2b1f3b524e14be9873a494918b295ad | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.984ms
2025-09-10 15:45:58.638 | INFO     | 74a9087d3309450da8506fd00543dd01 | 成功认证Java用户: admin
2025-09-10 15:45:58.640 | INFO     | 74a9087d3309450da8506fd00543dd01 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.138ms
2025-09-10 15:47:21.640 | INFO     | 567c358946524fce9a63c80da030afc9 | 成功认证Java用户: admin
2025-09-10 15:47:21.641 | INFO     | 567c358946524fce9a63c80da030afc9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.601ms
2025-09-10 15:48:21.638 | INFO     | c0133e43714447088d2a2a4a582d1207 | 成功认证Java用户: admin
2025-09-10 15:48:21.640 | INFO     | c0133e43714447088d2a2a4a582d1207 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.514ms
2025-09-10 15:49:13.021 | INFO     | 5e8e6a8ec9a2411e883ab59aa3cf2472 | 成功认证Java用户: admin
2025-09-10 15:49:13.232 | INFO     | 5e8e6a8ec9a2411e883ab59aa3cf2472 | 127.0.0.1       | POST     | 404    | /api/v1/iot/unified_chat/stream | 213.183ms
2025-09-10 15:49:21.632 | INFO     | 715eea208b7c41b293dc207dd2193b28 | 成功认证Java用户: admin
2025-09-10 15:49:21.634 | INFO     | 715eea208b7c41b293dc207dd2193b28 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.067ms
2025-09-10 15:49:28.455 | INFO     | f99548ee3c0a49789afbcfe7534e2e46 | 成功认证Java用户: admin
2025-09-10 15:49:28.457 | INFO     | f99548ee3c0a49789afbcfe7534e2e46 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.714ms
2025-09-10 15:49:56.346 | INFO     | 7e3d742e3ddf45ae98310dc7cdb57ae3 | 成功认证Java用户: admin
2025-09-10 15:49:56.350 | INFO     | 7e3d742e3ddf45ae98310dc7cdb57ae3 | 127.0.0.1       | POST     | 404    | /api/v1/iot/unified_chat/stream | 5.325ms
2025-09-10 15:49:58.632 | INFO     | 3bfc1db69f5d46dab21982bfed4ad5ea | 成功认证Java用户: admin
2025-09-10 15:49:58.633 | INFO     | 3bfc1db69f5d46dab21982bfed4ad5ea | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.365ms
2025-09-10 15:50:28.637 | INFO     | 7353e3e398144b9cb7c6f026bbcd7070 | 成功认证Java用户: admin
2025-09-10 15:50:28.638 | INFO     | 7353e3e398144b9cb7c6f026bbcd7070 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.196ms
2025-09-10 15:51:21.634 | INFO     | 092ef7b357e344469d5e28430736b441 | 成功认证Java用户: admin
2025-09-10 15:51:21.636 | INFO     | 092ef7b357e344469d5e28430736b441 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.582ms
2025-09-10 15:52:06.745 | INFO     | 9fdb93f66c534f2c8fb59d09eaee5343 | 成功认证Java用户: admin
2025-09-10 15:52:06.748 | INFO     | 9fdb93f66c534f2c8fb59d09eaee5343 | 127.0.0.1       | POST     | 404    | /api/v1/iot/unified_chat/stream | 5.083ms
2025-09-10 15:52:21.628 | INFO     | 28e8b179fbf440bbbf574c194735d3eb | 成功认证Java用户: admin
2025-09-10 15:52:21.630 | INFO     | 28e8b179fbf440bbbf574c194735d3eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.393ms
2025-09-10 15:52:31.863 | INFO     | fbcf918e86874528ac88e33860b3905a | 成功认证Java用户: admin
2025-09-10 15:52:31.867 | INFO     | fbcf918e86874528ac88e33860b3905a | 127.0.0.1       | POST     | 404    | /api/v1/iot/unified_chat/stream | 5.394ms
2025-09-10 15:53:21.638 | INFO     | 8633b8e95c8943178edd7a035968e285 | 成功认证Java用户: admin
2025-09-10 15:53:21.640 | INFO     | 8633b8e95c8943178edd7a035968e285 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.472ms
2025-09-10 15:54:21.636 | INFO     | e83f27c5cfc945959db82206ede8e8e3 | 成功认证Java用户: admin
2025-09-10 15:54:21.637 | INFO     | e83f27c5cfc945959db82206ede8e8e3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.962ms
2025-09-10 15:54:25.406 | INFO     | 9386d7d04e704cd9901363c560da4364 | 成功认证Java用户: admin
2025-09-10 15:54:25.408 | INFO     | 9386d7d04e704cd9901363c560da4364 | 用户 1 发起统一流式聊天
2025-09-10 15:54:25.409 | INFO     | 9386d7d04e704cd9901363c560da4364 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 3.792ms
2025-09-10 15:54:26.436 | INFO     | 9386d7d04e704cd9901363c560da4364 | 初始化LangGraph智能体服务...
2025-09-10 15:54:26.436 | INFO     | 9386d7d04e704cd9901363c560da4364 | 加载了 3 个工具
2025-09-10 15:54:26.439 | INFO     | 9386d7d04e704cd9901363c560da4364 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:54:26.439 | INFO     | 9386d7d04e704cd9901363c560da4364 | LangGraph智能体服务初始化完成
2025-09-10 15:54:26.467 | INFO     | 9386d7d04e704cd9901363c560da4364 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:54:31.519 | INFO     | 9386d7d04e704cd9901363c560da4364 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:55:21.629 | INFO     | e7760913eb0246f7bad93307338e68a4 | 成功认证Java用户: admin
2025-09-10 15:55:21.631 | INFO     | e7760913eb0246f7bad93307338e68a4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.467ms
2025-09-10 15:56:21.640 | INFO     | 59f4e33d7ac249168270460db6c1de32 | 成功认证Java用户: admin
2025-09-10 15:56:21.642 | INFO     | 59f4e33d7ac249168270460db6c1de32 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.016ms
2025-09-10 15:57:21.635 | INFO     | dfb4f05117ed469a9bcb4a87798f81b6 | 成功认证Java用户: admin
2025-09-10 15:57:21.638 | INFO     | dfb4f05117ed469a9bcb4a87798f81b6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.034ms
2025-09-10 15:57:51.171 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 15:58:18.346 | INFO     | c3e90cc823a1419ab9087ac400a0300c | 成功认证Java用户: admin
2025-09-10 15:58:18.349 | INFO     | c3e90cc823a1419ab9087ac400a0300c | 用户 1 发起统一流式聊天
2025-09-10 15:58:18.350 | INFO     | c3e90cc823a1419ab9087ac400a0300c | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 11.536ms
2025-09-10 15:58:19.360 | INFO     | c3e90cc823a1419ab9087ac400a0300c | 初始化LangGraph智能体服务...
2025-09-10 15:58:19.360 | INFO     | c3e90cc823a1419ab9087ac400a0300c | 加载了 3 个工具
2025-09-10 15:58:19.364 | INFO     | c3e90cc823a1419ab9087ac400a0300c | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 15:58:19.364 | INFO     | c3e90cc823a1419ab9087ac400a0300c | LangGraph智能体服务初始化完成
2025-09-10 15:58:20.317 | INFO     | c3e90cc823a1419ab9087ac400a0300c | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 15:58:21.643 | INFO     | 15a5da5670cd48ffb0db8353a42f34c3 | 成功认证Java用户: admin
2025-09-10 15:58:21.646 | INFO     | 15a5da5670cd48ffb0db8353a42f34c3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.030ms
2025-09-10 15:58:25.300 | INFO     | c3e90cc823a1419ab9087ac400a0300c | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:05:26.975 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 16:05:31.496 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | 成功认证Java用户: admin
2025-09-10 16:05:31.510 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | 用户 1 发起统一流式聊天
2025-09-10 16:05:31.512 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 26.878ms
2025-09-10 16:05:32.520 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | 初始化LangGraph智能体服务...
2025-09-10 16:05:32.520 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | 加载了 3 个工具
2025-09-10 16:05:32.525 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:05:32.525 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | LangGraph智能体服务初始化完成
2025-09-10 16:05:34.148 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:05:39.031 | INFO     | 4f6c59b6c32844ce84d3dd064c733e92 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:05:42.141 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | 成功认证Java用户: admin
2025-09-10 16:05:42.143 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | 用户 1 发起统一流式聊天
2025-09-10 16:05:42.143 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.291ms
2025-09-10 16:05:43.168 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | 初始化LangGraph智能体服务...
2025-09-10 16:05:43.168 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | 加载了 3 个工具
2025-09-10 16:05:43.172 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:05:43.172 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | LangGraph智能体服务初始化完成
2025-09-10 16:05:43.196 | INFO     | 05eecd28cb48421ca66ab9efac4ffad7 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:06:21.641 | INFO     | 16739db5119243b081a5b0953c9a44b2 | 成功认证Java用户: admin
2025-09-10 16:06:21.643 | INFO     | 16739db5119243b081a5b0953c9a44b2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.785ms
2025-09-10 16:07:16.248 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | 成功认证Java用户: admin
2025-09-10 16:07:16.249 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | 用户 1 发起统一流式聊天
2025-09-10 16:07:16.385 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 139.625ms
2025-09-10 16:07:17.406 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | 初始化LangGraph智能体服务...
2025-09-10 16:07:17.407 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | 加载了 3 个工具
2025-09-10 16:07:17.410 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:07:17.410 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | LangGraph智能体服务初始化完成
2025-09-10 16:07:17.436 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:07:20.789 | INFO     | 87bb6dbf80544da0894d90de0e0c6d75 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:07:21.639 | INFO     | 5d3e12199e654a3d95bb1765297dfe8a | 成功认证Java用户: admin
2025-09-10 16:07:21.641 | INFO     | 5d3e12199e654a3d95bb1765297dfe8a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.201ms
2025-09-10 16:07:25.014 | INFO     | 390e08f97db244839f496420c7ca6ffa | 成功认证Java用户: admin
2025-09-10 16:07:25.016 | INFO     | 390e08f97db244839f496420c7ca6ffa | 用户 1 发起统一流式聊天
2025-09-10 16:07:25.016 | INFO     | 390e08f97db244839f496420c7ca6ffa | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 3.554ms
2025-09-10 16:07:26.040 | INFO     | 390e08f97db244839f496420c7ca6ffa | 初始化LangGraph智能体服务...
2025-09-10 16:07:26.041 | INFO     | 390e08f97db244839f496420c7ca6ffa | 加载了 3 个工具
2025-09-10 16:07:26.043 | INFO     | 390e08f97db244839f496420c7ca6ffa | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:07:26.044 | INFO     | 390e08f97db244839f496420c7ca6ffa | LangGraph智能体服务初始化完成
2025-09-10 16:07:26.067 | INFO     | 390e08f97db244839f496420c7ca6ffa | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:08:21.639 | INFO     | 8fdc99d648ca436d97446b59e70e3ef3 | 成功认证Java用户: admin
2025-09-10 16:08:21.641 | INFO     | 8fdc99d648ca436d97446b59e70e3ef3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.073ms
2025-09-10 16:08:35.381 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 16:09:21.636 | INFO     | 79ea6e8217924332a2faf5c4967ba6c7 | 成功认证Java用户: admin
2025-09-10 16:09:21.638 | INFO     | 79ea6e8217924332a2faf5c4967ba6c7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 10.267ms
2025-09-10 16:10:15.950 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 16:10:21.653 | INFO     | 41bada52554f450aa432b995b2f920be | 成功认证Java用户: admin
2025-09-10 16:10:21.657 | INFO     | 41bada52554f450aa432b995b2f920be | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 15.933ms
2025-09-10 16:11:21.628 | INFO     | d724a55c29a14813b03c18a08a0dbe34 | 成功认证Java用户: admin
2025-09-10 16:11:21.630 | INFO     | d724a55c29a14813b03c18a08a0dbe34 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.691ms
2025-09-10 16:12:04.989 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 16:12:21.641 | INFO     | 562665b6c3884abca51cdc25471e622c | 成功认证Java用户: admin
2025-09-10 16:12:21.643 | INFO     | 562665b6c3884abca51cdc25471e622c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 9.589ms
2025-09-10 16:12:59.794 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 16:13:01.634 | INFO     | 4b1fa74e0107413b915f534994126b1e | 成功认证Java用户: admin
2025-09-10 16:13:01.638 | INFO     | 4b1fa74e0107413b915f534994126b1e | 用户 1 发起统一流式聊天
2025-09-10 16:13:01.639 | INFO     | 4b1fa74e0107413b915f534994126b1e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 12.487ms
2025-09-10 16:13:02.655 | INFO     | 4b1fa74e0107413b915f534994126b1e | 初始化LangGraph智能体服务...
2025-09-10 16:13:02.656 | INFO     | 4b1fa74e0107413b915f534994126b1e | 加载了 3 个工具
2025-09-10 16:13:02.660 | INFO     | 4b1fa74e0107413b915f534994126b1e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:13:02.661 | INFO     | 4b1fa74e0107413b915f534994126b1e | LangGraph智能体服务初始化完成
2025-09-10 16:13:04.300 | INFO     | 4b1fa74e0107413b915f534994126b1e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:13:09.160 | INFO     | 4b1fa74e0107413b915f534994126b1e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:13:12.895 | INFO     | a29a9afc1ce245e494865a18551e3a67 | 成功认证Java用户: admin
2025-09-10 16:13:12.896 | INFO     | a29a9afc1ce245e494865a18551e3a67 | 用户 1 发起统一流式聊天
2025-09-10 16:13:12.896 | INFO     | a29a9afc1ce245e494865a18551e3a67 | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 3.157ms
2025-09-10 16:13:13.924 | INFO     | a29a9afc1ce245e494865a18551e3a67 | 初始化LangGraph智能体服务...
2025-09-10 16:13:13.924 | INFO     | a29a9afc1ce245e494865a18551e3a67 | 加载了 3 个工具
2025-09-10 16:13:13.927 | INFO     | a29a9afc1ce245e494865a18551e3a67 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:13:13.927 | INFO     | a29a9afc1ce245e494865a18551e3a67 | LangGraph智能体服务初始化完成
2025-09-10 16:13:13.951 | INFO     | a29a9afc1ce245e494865a18551e3a67 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:13:21.638 | INFO     | 5ffc8f976d9a4dbeb24de1cc4270ed83 | 成功认证Java用户: admin
2025-09-10 16:13:21.758 | INFO     | 5ffc8f976d9a4dbeb24de1cc4270ed83 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 121.177ms
2025-09-10 16:14:21.638 | INFO     | b75e24c2e61d4101b73f90de221a7dab | 成功认证Java用户: admin
2025-09-10 16:14:21.639 | INFO     | b75e24c2e61d4101b73f90de221a7dab | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.683ms
2025-09-10 16:15:11.798 | INFO     | ab3dfc6bc81b4910ae74aea928982d7a | 成功认证Java用户: admin
2025-09-10 16:15:11.801 | INFO     | ab3dfc6bc81b4910ae74aea928982d7a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 6.016ms
2025-09-10 16:15:27.801 | INFO     | 3e1c9d36da3845d892a84d73024932ec | 成功认证Java用户: admin
2025-09-10 16:15:27.803 | INFO     | 3e1c9d36da3845d892a84d73024932ec | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.053ms
2025-09-10 16:15:58.629 | INFO     | 198b63cf4f7244faa684cb21192ce961 | 成功认证Java用户: admin
2025-09-10 16:15:58.630 | INFO     | 198b63cf4f7244faa684cb21192ce961 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.770ms
2025-09-10 16:16:28.631 | INFO     | 4bf40009bfed40d4b64896c8089fb764 | 成功认证Java用户: admin
2025-09-10 16:16:28.633 | INFO     | 4bf40009bfed40d4b64896c8089fb764 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.677ms
2025-09-10 16:16:32.552 | INFO     | af7b0e13fe504943bfce7786257269be | 成功认证Java用户: admin
2025-09-10 16:16:32.554 | INFO     | af7b0e13fe504943bfce7786257269be | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.147ms
2025-09-10 16:16:54.231 | INFO     | 5e5ddc1e3ad44e3d8d87eb6a6d5476c3 | 成功认证Java用户: admin
2025-09-10 16:16:54.233 | INFO     | 5e5ddc1e3ad44e3d8d87eb6a6d5476c3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.306ms
2025-09-10 16:17:02.627 | INFO     | 1a1b845bfa2040808e285fb506aedab1 | 成功认证Java用户: admin
2025-09-10 16:17:02.629 | INFO     | 1a1b845bfa2040808e285fb506aedab1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.139ms
2025-09-10 16:17:15.277 | INFO     | 937ae9aff09343f8a1cd7bd4f9a01105 | 成功认证Java用户: admin
2025-09-10 16:17:15.279 | INFO     | 937ae9aff09343f8a1cd7bd4f9a01105 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.116ms
2025-09-10 16:17:21.626 | INFO     | ae37474c8d124abb81d734d5be8d4ba3 | 成功认证Java用户: admin
2025-09-10 16:17:21.628 | INFO     | ae37474c8d124abb81d734d5be8d4ba3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.296ms
2025-09-10 16:17:24.626 | INFO     | 49ad0252be7141fe8192d12bd4f0808a | 成功认证Java用户: admin
2025-09-10 16:17:24.628 | INFO     | 49ad0252be7141fe8192d12bd4f0808a | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.188ms
2025-09-10 16:17:32.628 | INFO     | 8e447e57006449378be0b0db3ae359c5 | 成功认证Java用户: admin
2025-09-10 16:17:32.630 | INFO     | 8e447e57006449378be0b0db3ae359c5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.264ms
2025-09-10 16:17:45.628 | INFO     | 204afd022e86472eaf603f8f5298e680 | 成功认证Java用户: admin
2025-09-10 16:17:45.631 | INFO     | 204afd022e86472eaf603f8f5298e680 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.843ms
2025-09-10 16:17:46.122 | INFO     | 787cae8eef9e4a44ac17dd28bf67c75e | 成功认证Java用户: admin
2025-09-10 16:17:46.124 | INFO     | 787cae8eef9e4a44ac17dd28bf67c75e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.113ms
2025-09-10 16:17:54.637 | INFO     | aa2db2b7d4c346fd9b9bca6e1bb5f81c | 成功认证Java用户: admin
2025-09-10 16:17:54.639 | INFO     | aa2db2b7d4c346fd9b9bca6e1bb5f81c | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.114ms
2025-09-10 16:18:02.631 | INFO     | 8d8555a236b245e78029c424e4ef93d5 | 成功认证Java用户: admin
2025-09-10 16:18:02.634 | INFO     | 8d8555a236b245e78029c424e4ef93d5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.394ms
2025-09-10 16:18:15.636 | INFO     | 7aef4fcbe8674312840266dc0b52ac27 | 成功认证Java用户: admin
2025-09-10 16:18:15.638 | INFO     | 7aef4fcbe8674312840266dc0b52ac27 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.227ms
2025-09-10 16:18:16.637 | INFO     | e191e6899bee460a9a8947d07ba647d2 | 成功认证Java用户: admin
2025-09-10 16:18:16.638 | INFO     | e191e6899bee460a9a8947d07ba647d2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.701ms
2025-09-10 16:18:20.312 | INFO     | a7b9da6fae0c4b9790cac91f7a7a8530 | 成功认证Java用户: admin
2025-09-10 16:18:20.314 | INFO     | a7b9da6fae0c4b9790cac91f7a7a8530 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.052ms
2025-09-10 16:18:29.451 | INFO     | 45689aa128cd4b628349c8e3dce6dfbe | 成功认证Java用户: admin
2025-09-10 16:18:29.453 | INFO     | 45689aa128cd4b628349c8e3dce6dfbe | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.044ms
2025-09-10 16:18:39.472 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | 成功认证Java用户: admin
2025-09-10 16:18:39.475 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | 用户 1 发起统一流式聊天
2025-09-10 16:18:39.475 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | 192.168.66.13   | POST     | 200    | /api/iot/v1/chat/chat/stream | 4.967ms
2025-09-10 16:18:40.489 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | 初始化LangGraph智能体服务...
2025-09-10 16:18:40.489 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | 加载了 3 个工具
2025-09-10 16:18:40.492 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 16:18:40.492 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | LangGraph智能体服务初始化完成
2025-09-10 16:18:40.525 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:18:45.976 | INFO     | cbba278e699e4567af64f88b3ce6ee0e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 16:18:59.471 | INFO     | 5725f37e34184ea3966c650a9c06a4ec | 成功认证Java用户: admin
2025-09-10 16:18:59.473 | INFO     | 5725f37e34184ea3966c650a9c06a4ec | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.226ms
2025-09-10 16:19:29.470 | INFO     | c2313a1df758431088808187552c0c0b | 成功认证Java用户: admin
2025-09-10 16:19:29.471 | INFO     | c2313a1df758431088808187552c0c0b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.941ms
2025-09-10 16:19:59.624 | INFO     | 974ccaa8804a4366851e969295b494e8 | 成功认证Java用户: admin
2025-09-10 16:19:59.626 | INFO     | 974ccaa8804a4366851e969295b494e8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.250ms
2025-09-10 16:20:29.636 | INFO     | 3065769164a6404c992c92f599d20c39 | 成功认证Java用户: admin
2025-09-10 16:20:29.638 | INFO     | 3065769164a6404c992c92f599d20c39 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.780ms
2025-09-10 16:20:59.637 | INFO     | 5ab9a561cef74ae387c4ab055122319d | 成功认证Java用户: admin
2025-09-10 16:20:59.638 | INFO     | 5ab9a561cef74ae387c4ab055122319d | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 2.829ms
2025-09-10 16:21:29.629 | INFO     | c5cfeea662854da4a33d9ad882365229 | 成功认证Java用户: admin
2025-09-10 16:21:29.631 | INFO     | c5cfeea662854da4a33d9ad882365229 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.076ms
2025-09-10 16:22:21.631 | INFO     | 13801b5345344e88af2fbd850b2ebb38 | 成功认证Java用户: admin
2025-09-10 16:22:21.633 | INFO     | 13801b5345344e88af2fbd850b2ebb38 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.599ms
2025-09-10 16:23:21.632 | INFO     | fd845a436f3b4e568c8d39f1e7e18aff | 成功认证Java用户: admin
2025-09-10 16:23:21.634 | INFO     | fd845a436f3b4e568c8d39f1e7e18aff | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.675ms
2025-09-10 16:24:21.627 | INFO     | 235874084e3a49d4acf7d9222182919e | 成功认证Java用户: admin
2025-09-10 16:24:21.629 | INFO     | 235874084e3a49d4acf7d9222182919e | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.845ms
2025-09-10 16:25:21.637 | INFO     | 35748cf9456340e593a90153f3abf0a1 | 成功认证Java用户: admin
2025-09-10 16:25:21.639 | INFO     | 35748cf9456340e593a90153f3abf0a1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 4.355ms
2025-09-10 16:25:29.630 | INFO     | 0423417e01e74c13a4c082ef1dd63d3b | 成功认证Java用户: admin
2025-09-10 16:25:29.632 | INFO     | 0423417e01e74c13a4c082ef1dd63d3b | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.341ms
2025-09-10 16:25:59.636 | INFO     | 51fea49bf4014274a08b887ce3078ea2 | 成功认证Java用户: admin
2025-09-10 16:25:59.638 | INFO     | 51fea49bf4014274a08b887ce3078ea2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.338ms
2025-09-10 16:27:21.638 | INFO     | 0241a7814ea04488a66dccd74c0d1279 | 成功认证Java用户: admin
2025-09-10 16:27:21.640 | INFO     | 0241a7814ea04488a66dccd74c0d1279 | 192.168.66.13   | GET      | 200    | /api/iot/v1/chat/health | 3.611ms
